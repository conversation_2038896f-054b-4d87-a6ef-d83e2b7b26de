#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEditor.Experimental.SceneManagement;
using System.Collections.Generic;
using System.IO;
using System;
using Newtonsoft.Json;
using System.Linq;
using MessagePack;
using System.Diagnostics;

public static class MapBlockExporter
{
    private const int gridSize = 32;
    private const float cellSize = 1f;
    // private static readonly string[] waterMaterialKeywords = new[] {
    //     "pond_on", "pond_edge", "sea_line02", "pond_line", "pond_on_edge", "sea_un", "sea_on", "sea_rock", "sea_line", "sea_rock_m", "river_r", "river"
    // };
    // private static readonly string[] isOnWaterMaterialKeywords = new[] {
    //     "sea_un", "sea_on", "river_r", "river", "pond_on", "pond_edge", "pond_on_edge"
    // };
    private static HashSet<string> transmitMapNames = new HashSet<string>();
    private static readonly string[] waterMaterialKeywords = new[] {
        "pond_on", "pond_edge", "sea_line02", "pond_on_edge", "sea_un", "sea_on", "sea_line", "river_r", "river"
    };
    private static readonly List<string> prefabTypeNames = new() { "HeartGold", "Platinum", "White2" };
    // private static readonly string[] isOnWaterMaterialKeywords = new[] {
    //     "sea_un", "sea_on", "river_r", "river", "pond_on", "pond_edge", "pond_on_edge"
    // };

    private static readonly string[] grassMaterialKeywords = new[] {
            "egrass"
    }; //草地动画

    [MenuItem("Tools/Map/导出所有的地图数据Test")]
    public static void ExportAllMapData()
    {
        var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
        GameObject prefabRoot = PrefabStageUtility.GetCurrentPrefabStage()?.prefabContentsRoot;
        if (prefabRoot == null)
        {
            EditorUtility.DisplayDialog("提示", "请在Prefab模式下打开一个Prefab进行导出。", "确定");
            return;
        }

        string folderPath = Application.dataPath + "/AssetRaw/Map/Json";
        string editfolderPath = "Assets/Editor/";
        // if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

        string outputJsonPath = Path.Combine(editfolderPath, prefabRoot.name + "_test" + ".json");
        string outputBytePath = Path.Combine(folderPath, prefabRoot.name + ".bytes");


        var mapName = MaterialWalkableEditor.GetMapTypeName(prefabRoot, 0);
        if (string.IsNullOrEmpty(mapName))
        {
            EditorUtility.DisplayDialog("错误", "❌ 选中的 GameObject 不在任何已知地图中", "确定");
            return;
        }
        MaterialWalkableData walkableData = MaterialWalkableEditor.LoadData(mapName);

        // MapData mapdata = new MapData();

        List<string> prefabTypeNames = new() { "HeartGold", "Platinum", "White2" };
        // var npcList = new List<MapNPCInfoPack>();
        // var initPostions = new List<MapInitPostion>();

        MapDataPack mapDataPack = new MapDataPack {
            gridSize = gridSize,
            cellSize = cellSize,
            mainBlocks = new Dictionary<Vector2Int, MapBlock>(),
            initPostions = new Dictionary<Vector2Int, MapInitPostionPack>(),
            npcs = new Dictionary<Vector2Int, List<MapNPCInfoPack>>(),
            otherUnits = new Dictionary<Vector2Int, List<MapOtherUnitInfoPack>>(),
            runtimeObstacles = new Dictionary<Vector2Int, List<MapRuntimeObstacleInfoPack>>(),
            triggerEventComponents = new Dictionary<Vector2Int, List<MapTriggerEventComponentInfoPack>>()
        };
        // tile数据
        // var mapCellDatas = new MapCellData
        // {
        // };
        // mapDataPack.mapCellDatas = mapCellDatas;
        // Transform eventObjectsTransform = null;
        // foreach (Transform topLevel in prefabRoot.transform)
        // {
        //     if (topLevel.name.EndsWith("EventObjects"))
        //     {
        //         eventObjectsTransform = topLevel;
        //         eventObjectsTransform.gameObject.SetActive(false);
        //     }
        // }
        foreach (Transform topLevel in prefabRoot.transform)
        {
            if(TrySetInitPoint(mapDataPack, topLevel)) {
                continue;
            }
            // if (!topLevel.name.EndsWith("Main"))
            // {
            //     // Debug.Log("Main");
            //     continue;
            // }
            foreach (Transform child in topLevel.transform)
            {
                if (child == topLevel) continue;
                if(TrySetInitPoint(mapDataPack, child)) {
                    continue;
                }
                if(TrySetNpcInfo(mapDataPack, child)) {
                    continue;
                }
                if(TrySetMapCellInfos(walkableData, mapDataPack, child)) {
                    continue;
                }
                if(TrySetRuntimeEventObject(mapDataPack, child)) {
                    continue;
                }
                SetMapOtherUnit(mapDataPack, child);
                
            }
        }
        SaveAndLoadWithTiming(mapDataPack, outputBytePath);
        // MessagePack.Unity.MessagePackInitializer
        // 直接序列化成 MessagePack 二进制
        // byte[] bytes = MessagePackSerializer.Serialize(mapCellDatas);

        // File.WriteAllBytes(outputJsonPath, bytes);
        // AssetDatabase.Refresh();


        // bytes = File.ReadAllBytes(outputJsonPath);
        // MapCellData mapData = MessagePackSerializer.Deserialize<MapCellData>(bytes);
        var settings = new JsonSerializerSettings
        {
            Converters = new[] {
                new Vec3Conv(),
                //new StringEnumConverter(),
            },
        };
        // // string json = JsonUtility.ToJson(mapdata, true);
        // // File.WriteAllText(outputJsonPath, json);
        // // AssetDatabase.Refresh();

        // // EditorUtility.DisplayDialog("✅ 导出完成", $"已保存至:\n{outputJsonPath}", "好");
        // // var myObjectINeedToSerialize = new Vector3(1, 2, 3);

        string json = JsonConvert.SerializeObject(mapDataPack, settings);
        File.WriteAllText(outputJsonPath, json);
        AssetDatabase.Refresh();

        EditorUtility.DisplayDialog("✅ 导出完成", $"已保存至:\n{outputJsonPath}", "好");
    }
    public static bool TrySetRuntimeEventObject(MapDataPack dataPack, Transform child) {
        if (!(child.parent.name.EndsWith("EventObjects")))
        {
            return false;
        }
        if(child.parent == child) {
            return false;
        }
        // Transform eventObjectsTransform = child;
        // foreach (Transform topLevel in prefabRoot.transform)
        // {
        //     if (topLevel.name.EndsWith("EventObjects"))
        //     {
        //         eventObjectsTransform = topLevel;
        //         eventObjectsTransform.gameObject.SetActive(false);
        //     }
        // }
        // var obstacleList = new List<MapRuntimeObstacleData>();
        // var eventComponentList = new List<MapTriggerEventComponentInfo>();
        // if (eventObjectsTransform != null)
        // {
        //     eventObjectsTransform.gameObject.SetActive(true);
        //     foreach (Transform child in eventObjectsTransform)
        //     {
                
        //     }
        //     mapdata.runtimeObstacles = obstacleList;
        //     mapdata.triggerEventComponents = eventComponentList;
        // }
        // if (child == eventObjectsTransform) continue;
        // string prefabAddress = "";
        // GameObject prefabInstance = PrefabUtility.GetNearestPrefabInstanceRoot(child.gameObject);
        // if (prefabInstance != null)
        // {
        //     prefabAddress = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(prefabInstance);
        //     bool matched = false;
        //     foreach (var name in prefabTypeNames)
        //     {
        //         if (prefabAddress.Contains(name))
        //         {
        //             string fileName = Path.GetFileNameWithoutExtension(prefabAddress);
        //             prefabAddress = $"{name}_{fileName}";
        //             matched = true;
        //             break;
        //         }
        //     }
        //     if (!matched)
        //         prefabAddress = Path.GetFileNameWithoutExtension(prefabAddress);
        // }
        var eventComponent = child.GetComponent<MapTriggerEventComponent>();
        var eventComponentCollider = child.GetComponent<BoxCollider>();

        if (eventComponent != null)
        {
            if (string.IsNullOrEmpty(eventComponent.eventName) || eventComponent.triggerEventType == MapTriggerEventType.None)
            {
                EditorUtility.DisplayDialog("导出错误", $"空的事件点名称: {child.name}", "确定");
                throw new Exception("空的事件点名称");
            }
            if (eventComponentCollider == null)
            {
                EditorUtility.DisplayDialog("导出错误", $"事件点没有BoxCollider: {child.name}", "确定");
                throw new Exception("事件点没有BoxCollider");
            }
            MapTriggerEventComponentInfoPack eventComponentInfo = null;
            var prefabAddress = GetPrefabAddress(child);
            eventComponentInfo = new MapTriggerEventComponentInfoPack
            {
                eventName = eventComponent.eventName,
                eventValue = eventComponent.eventValue,
                prefab = string.IsNullOrEmpty(prefabAddress) ? child.name : prefabAddress,
                triggerEventType = eventComponent.triggerEventType,
                position = eventComponent.transform.localPosition,
                localScale = eventComponent.transform.localScale,
                boxColliderSize = eventComponentCollider.size
            };
            var oprationDatas = new List<MapYarnOprationInfoPack>();
            var yarnTitleList = new List<string>();
            var yarnStartOprationTypeList = new List<MapYarnOprationType>();
            var yarnEndOprationTypeList = new List<MapYarnOprationType>();
            var yarnStartOprationValues = new List<string>();
            var yarnEndOprationValues = new List<string>();
            if (!string.IsNullOrEmpty(eventComponent.yarnTitle))
            {
                if (!yarnTitleList.Contains(eventComponent.yarnTitle))
                {
                    yarnTitleList.Add(eventComponent.yarnTitle);
                }
                yarnStartOprationTypeList.Add(eventComponent.yarnStartOprationType);
                yarnStartOprationValues.Add("");
                yarnEndOprationTypeList.Add(eventComponent.yarnEndOprationType);
                yarnEndOprationValues.Add("");
            }
            // if (eventComponent.yarnEndOprationType != MapYarnOprationType.None)
            // {
            //     if (!yarnTitleList.Contains(eventComponent.yarnTitle))
            //     {
            //         yarnTitleList.Add(eventComponent.yarnTitle);
            //     }
            //     yarnEndOprationTypeList.Add(eventComponent.yarnEndOprationType);
            //     yarnEndOprationValues.Add("");
            // }
            yarnTitleList.AddRange(eventComponent.yarnTitleList);
            yarnStartOprationTypeList.AddRange(eventComponent.yarnStartOprationTypeList);
            yarnEndOprationTypeList.AddRange(eventComponent.yarnEndOprationTypeList);
            yarnStartOprationValues.AddRange(eventComponent.yarnStartOprationValues);
            yarnEndOprationValues.AddRange(eventComponent.yarnEndOprationValues);
            if (yarnTitleList.Count != yarnStartOprationTypeList.Count ||
            yarnTitleList.Count != yarnEndOprationTypeList.Count ||
            yarnTitleList.Count != yarnStartOprationValues.Count ||
            yarnTitleList.Count != yarnEndOprationValues.Count)
            {
                throw new Exception("yarnTitle 数量不对");
            }
            for (int i = 0; i < yarnTitleList.Count; i++)
            {
                oprationDatas.Add(new MapYarnOprationInfoPack
                {
                    yarnTitle = yarnTitleList[i],
                    yarnStartOprationType = yarnStartOprationTypeList[i],
                    yarnEndOprationType = yarnEndOprationTypeList[i],
                    yarnStartOprationValue = yarnStartOprationValues[i],
                    yarnEndOprationValue = yarnEndOprationValues[i]
                });
            }
            eventComponentInfo.oprationDatas = oprationDatas;
            var blockKey = WorldToBlockCoord(eventComponentInfo.position); //不用像Main的那么精准
            // eventComponentList.Add(eventComponentInfo);
            if(dataPack.triggerEventComponents.ContainsKey(blockKey)) {
                dataPack.triggerEventComponents[blockKey].Add(eventComponentInfo);
            } else {
                dataPack.triggerEventComponents.Add(blockKey, new List<MapTriggerEventComponentInfoPack> { eventComponentInfo });
            }
            // dataPack.triggerEventComponents.Add(blockKey, eventComponentInfo);
            return true;
        }

        var obstacleComponent = child.GetComponent<MapRuntimeObstacle>();
        var obstacleComponentCollider = child.GetComponent<BoxCollider>();

        // var obstacleComponent = child.GetComponent<NinMapNPCComponent>();
        if (obstacleComponent != null)
        {
            var oprationDatas = new List<MapYarnOprationInfoPack>();
            var yarnTitleList = new List<string>();
            var yarnStartOprationTypeList = new List<MapYarnOprationType>();
            var yarnEndOprationTypeList = new List<MapYarnOprationType>();
            var yarnStartOprationValues = new List<string>();
            var yarnEndOprationValues = new List<string>();
            if (obstacleComponent.yarnStartOprationType != MapYarnOprationType.None)
            {
                if (!yarnTitleList.Contains(obstacleComponent.yarnTitle))
                {
                    yarnTitleList.Add(obstacleComponent.yarnTitle);
                }
                yarnStartOprationTypeList.Add(obstacleComponent.yarnStartOprationType);
                yarnStartOprationValues.Add("");
            }
            if (obstacleComponent.yarnEndOprationType != MapYarnOprationType.None)
            {
                if (!yarnTitleList.Contains(obstacleComponent.yarnTitle))
                {
                    yarnTitleList.Add(obstacleComponent.yarnTitle);
                }
                yarnEndOprationTypeList.Add(obstacleComponent.yarnEndOprationType);
                yarnEndOprationValues.Add("");
            }
            yarnTitleList.AddRange(obstacleComponent.yarnTitleList);
            yarnStartOprationTypeList.AddRange(obstacleComponent.yarnStartOprationTypeList);
            yarnEndOprationTypeList.AddRange(obstacleComponent.yarnEndOprationTypeList);
            yarnStartOprationValues.AddRange(obstacleComponent.yarnStartOprationValues);
            yarnEndOprationValues.AddRange(obstacleComponent.yarnEndOprationValues);
            if (yarnTitleList.Count != yarnStartOprationTypeList.Count ||
            yarnTitleList.Count != yarnEndOprationTypeList.Count ||
            yarnTitleList.Count != yarnStartOprationValues.Count ||
            yarnTitleList.Count != yarnEndOprationValues.Count)
            {
                throw new Exception("yarnTitle 数量不对");
            }
            for (int i = 0; i < yarnTitleList.Count; i++)
            {
                oprationDatas.Add(new MapYarnOprationInfoPack
                {
                    yarnTitle = yarnTitleList[i],
                    yarnStartOprationType = yarnStartOprationTypeList[i],
                    yarnEndOprationType = yarnEndOprationTypeList[i],
                    yarnStartOprationValue = yarnStartOprationValues[i],
                    yarnEndOprationValue = yarnEndOprationValues[i]
                });
            }
            MapRuntimeObstacleInfoPack obstacleComponentInfo = null;
            var prefabAddress = GetPrefabAddress(child);
            obstacleComponentInfo = new MapRuntimeObstacleInfoPack
            {
                name = obstacleComponent.name,
                position = obstacleComponent.transform.localPosition,
                scale = obstacleComponent.transform.localScale,
                defaultIsHiden = obstacleComponent.defaultIsHiden,
                oprationDatas = oprationDatas,
                prefab = string.IsNullOrEmpty(prefabAddress) ? child.name : prefabAddress,
            };
            var blockKey = WorldToBlockCoord(obstacleComponentInfo.position); //不用像Main的那么精准
            if(dataPack.runtimeObstacles.ContainsKey(blockKey)) {
                dataPack.runtimeObstacles[blockKey].Add(obstacleComponentInfo);
            } else {
                dataPack.runtimeObstacles.Add(blockKey, new List<MapRuntimeObstacleInfoPack> { obstacleComponentInfo });
            }
            // dataPack.runtimeObstacles.Add(blockKey, obstacleComponentInfo);
            // obstacleList.Add(obstacleComponentInfo);
            // continue;
            return true;
        }
        return false;
    }
    public static void SetMapOtherUnit(MapDataPack dataPack, Transform child) {
        MapTransmitPointInfoPack transmitMapInfo = null;
        var transmitComponent = child.GetComponent<TransmitMapComponent>();
        // var transmitBoxCollider = child.GetComponent<BoxCollider>();

        if (transmitComponent != null)
        {
            if (string.IsNullOrEmpty(transmitComponent.Name))
            {
                EditorUtility.DisplayDialog("导出错误", $"空的传送点名称: {child.name}", "确定");
                throw new Exception("空的传送点名称");
            }

            if (!transmitMapNames.Add(transmitComponent.Name))
            {
                EditorUtility.DisplayDialog("导出错误", $"重复的传送点名称: {transmitComponent.Name}", "确定");
                throw new Exception("重复的传送点名称");
            }

            transmitMapInfo = new MapTransmitPointInfoPack
            {
                name = transmitComponent.Name,
                toMapName = transmitComponent.ToMapName,
                position = transmitComponent.transform.localPosition,
                localScale = transmitComponent.transform.localScale,
                boxColliderSize = transmitComponent.GetComponent<BoxCollider>().size,
                isHidenEffect = transmitComponent.isHidenEffect,
                isPokeCenter = transmitComponent.isPointCenter
            };
        }
        
        string prefabName = child.name;
        Vector3 pos = child.localPosition;
        pos.y += (Mathf.Abs(child.parent.localPosition.y) < 0.1f ? 0 : child.parent.localPosition.y);
        Vector3 rotation = child.localRotation.eulerAngles;
        Vector3 scale = child.localScale;
        var prefabAddress = GetPrefabAddress(child);
        var otherUnitInfo = new MapOtherUnitInfoPack
        {
            name = prefabName,
            position = pos,
            rotation = rotation,
            scale = scale,
            prefabAddress = string.IsNullOrEmpty(prefabAddress) ? prefabName : prefabAddress,
            transmitMapInfo = transmitMapInfo
        };
        var blockKey = WorldToBlockCoord(pos); //不用像Main的那么精准
        if(dataPack.otherUnits.ContainsKey(blockKey)) {
            dataPack.otherUnits[blockKey].Add(otherUnitInfo);
        } else {
            dataPack.otherUnits.Add(blockKey, new List<MapOtherUnitInfoPack> { otherUnitInfo });
        }
        // dataPack.otherUnits.Add(blockKey, otherUnitInfo);
        // return true;
    }
    public static bool TrySetNpcInfo(MapDataPack dataPack, Transform child) {
        var npcComponent = child.GetComponent<NinMapNPCComponent>();
        if (npcComponent != null)
        {
            string spriteName = "";
            SpriteRenderer sr = npcComponent.gameObject.GetComponent<SpriteRenderer>();
            if (sr != null)
            {
                if(sr.sprite == null) {
                    throw new Exception("SpriteRenderer sprite没有设置");
                }
                spriteName = sr.sprite.name;
                // Debug.Log("当前Sprite的名称: " + spriteName);

            }
            var oprationDatas = new List<MapYarnOprationInfoPack>();
            var yarnTitleList = new List<string>();
            var yarnStartOprationTypeList = new List<MapYarnOprationType>();
            var yarnEndOprationTypeList = new List<MapYarnOprationType>();
            var yarnStartOprationValues = new List<string>();
            var yarnEndOprationValues = new List<string>();
            if (npcComponent.yarnStartOprationType != MapYarnOprationType.None)
            {
                if (!yarnTitleList.Contains(npcComponent.yarnTitle))
                {
                    yarnTitleList.Add(npcComponent.yarnTitle);
                }
                yarnStartOprationTypeList.Add(npcComponent.yarnStartOprationType);
                yarnStartOprationValues.Add("");
            }
            if (npcComponent.yarnEndOprationType != MapYarnOprationType.None)
            {
                if (!yarnTitleList.Contains(npcComponent.yarnTitle))
                {
                    yarnTitleList.Add(npcComponent.yarnTitle);
                }
                yarnEndOprationTypeList.Add(npcComponent.yarnEndOprationType);
                yarnEndOprationValues.Add("");
            }
            yarnTitleList.AddRange(npcComponent.yarnTitleList);
            yarnStartOprationTypeList.AddRange(npcComponent.yarnStartOprationTypeList);
            yarnEndOprationTypeList.AddRange(npcComponent.yarnEndOprationTypeList);
            yarnStartOprationValues.AddRange(npcComponent.yarnStartOprationValues);
            yarnEndOprationValues.AddRange(npcComponent.yarnEndOprationValues);
            if (yarnTitleList.Count != yarnStartOprationTypeList.Count ||
            yarnTitleList.Count != yarnEndOprationTypeList.Count ||
            yarnTitleList.Count != yarnStartOprationValues.Count ||
            yarnTitleList.Count != yarnEndOprationValues.Count)
            {
                throw new Exception("yarnTitle 数量不对");
            }
            for (int i = 0; i < yarnTitleList.Count; i++)
            {
                oprationDatas.Add(new MapYarnOprationInfoPack
                {
                    yarnTitle = yarnTitleList[i],
                    yarnStartOprationType = yarnStartOprationTypeList[i],
                    yarnEndOprationType = yarnEndOprationTypeList[i],
                    yarnStartOprationValue = yarnStartOprationValues[i],
                    yarnEndOprationValue = yarnEndOprationValues[i]
                });
            }
            var npcData = new MapNPCInfoPack
            {
                name = npcComponent.npcConfigName,
                imageName = spriteName,
                position = child.localPosition,
                defaultIsHiden = npcComponent.defaultIsHiden,
                oprationDatas = oprationDatas
            };
            var blockKey = WorldToBlockCoord(child.localPosition); //不用像Main的那么精准
            if(dataPack.npcs.ContainsKey(blockKey)) {
                dataPack.npcs[blockKey].Add(npcData);
            } else {
                dataPack.npcs.Add(blockKey, new List<MapNPCInfoPack> { npcData });
            }
            // dataPack.npcs.Add(blockKey, npcData);
            return true;
        }
        return false;
    }
    public static bool TrySetInitPoint(MapDataPack dataPack, Transform child) {
        var initPostion = child.GetComponent<MapInitPositionComponent>();
        if (initPostion != null)
        {
            var blockKey = WorldToBlockCoord(initPostion.transform.localPosition); //不用像Main的那么精准
            dataPack.initPostions.Add(blockKey, new MapInitPostionPack
            {
                // gen = initPostion.gen,
                tagName = initPostion.Name,
                position = initPostion.transform.localPosition,
                transmitMapInfo = new MapTransmitPointInfoPack
                {
                    name = initPostion.Name,
                    position = initPostion.transform.localPosition,
                    isPokeCenter = true
                }
            });
            return true;
        }
        return false;
    }
    public static bool TrySetMapCellInfos(MaterialWalkableData walkableData, MapDataPack mapCellDatas, Transform child) {
        if (!(child.parent.name.EndsWith("Main")))
        {
            return false;
        }
        // MapCellData
        string chunkName = child.name;
        Vector3 localPos = child.localPosition;
        Vector3 origin = child.transform.position - new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize);
        // Vector3 origin = WaterMapChecker.WaterWordOrigin(root.transform.position, new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize));
        // List<WaterCell> waterCells = new();


        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                Vector3 cellCenter = origin + new Vector3((x + 0.5f) * cellSize, 50f, (z + 0.5f) * cellSize); // 从高处往下射线
                UnityEngine.Debug.Log($"cellCenter: {cellCenter}");
                // Vector3 cellCenter = WaterMapChecker.WaterWordOrigin(origin,  new Vector3((x + 0.5f) * cellSize, 50f, (z + 0.5f) * cellSize));
                // bool isWater = false;
                // bool isWaterEdge = false;
                TileType tileType = TileType.NotWalkable;
                // bool hasTileInfo = false;
                string materialName = "(null)";
                string objectName = "(none)";
                MapCellInfo tileInfo = null;
                MapCellInfo parentTileInfo = null;
                var isBattle = false;

                // Ray ray = new Ray(cellCenter, Vector3.down);
                float maxDistance = 100f;

                // bool hitFound = false;
                // RaycastHit closestHit = new RaycastHit();
                // float closestDistance = float.MaxValue;
                var fff = 0.3f;
                // 检查当前中心 + 8 个邻居方向（共 9 个）
                Vector3[] directions = new Vector3[]
                {
                    Vector3.zero,                                   // 自身
                    // new Vector3(-fff * cellSize, 0, 0),            // 左
                    // new Vector3(fff * cellSize, 0, 0),             // 右
                    // new Vector3(0, 0, -fff * cellSize),            // 下
                    // new Vector3(0, 0, fff * cellSize),             // 上
                    // new Vector3(-fff * cellSize, 0, -fff * cellSize), // 左下
                    // new Vector3(fff * cellSize, 0, -fff * cellSize),  // 右下
                    // new Vector3(-fff * cellSize, 0, fff * cellSize),  // 左上
                    // new Vector3(fff * cellSize, 0, fff * cellSize),   // 右上
                };
                var surfaceY = 0f;
                foreach (var offset in directions)
                {
                    Vector3 probePoint = cellCenter + offset;
                    Ray ray = new Ray(probePoint, Vector3.down);

                    bool hitFound = false;
                    RaycastHit closestHit = new RaycastHit();
                    float closestDistance = float.MaxValue;

                    foreach (var collider in child.GetComponentsInChildren<Collider>(true))
                    {
                        if (collider.Raycast(ray, out RaycastHit hit, maxDistance))
                        {
                            float distance = hit.distance;
                            if (distance < closestDistance)
                            {
                                closestDistance = distance;
                                closestHit = hit;
                                hitFound = true;
                            }
                        }
                    }

                    if (hitFound)
                    {
                        objectName = closestHit.collider.name;
                        var item = closestHit.collider.GetComponent<MapTilePrefabComponent>();
                        var parentItem = child.GetComponent<MapTilePrefabComponent>();
                        if(parentItem != null) {
                            parentTileInfo = new MapCellInfo
                            {
                                Bgm = parentItem.Bgm,
                                RegionId = parentItem.RegionId,
                                AreaId = parentItem.AreaId,
                                BattleBgm = parentItem.BattleBgm,
                                TileType = parentItem.TileType
                            };
                        }
                        // var item = childItem.GetComponent<MapTilePrefabComponent>();
                        if (item == null)
                        {
                            item = parentItem;
                            // if (item == null) continue;
                        } else {
                            // hasTileInfo = true;
                            isBattle = item.IsBattle;
                            tileInfo = new MapCellInfo
                            {
                                Bgm = item.Bgm,
                                RegionId = item.RegionId,
                                AreaId = item.AreaId,
                                BattleBgm = item.BattleBgm,
                                TileType = item.TileType
                            };
                            if(parentTileInfo != null && tileInfo.Bgm == parentTileInfo.Bgm) {
                                tileInfo.Bgm = "";
                            }
                            if(parentTileInfo != null && tileInfo.BattleBgm == parentTileInfo.BattleBgm) {
                                tileInfo.BattleBgm = "";
                            }
                        }
                        var renderer = closestHit.collider.GetComponent<Renderer>();
                        if (renderer != null && renderer.sharedMaterial != null)
                        {
                            materialName = renderer.sharedMaterial.name.ToLowerInvariant();
                            if (waterMaterialKeywords.Contains(materialName))
                            {
                                tileType = TileType.Water;
                                // if (!isWaterEdge)
                                // {
                                //     bool onWater = false;
                                //     if (isOnWaterMaterialKeywords.Contains(materialName))
                                //     {
                                //         onWater = true;
                                //     }
                                //     if (!onWater)
                                //     {
                                //         isWaterEdge = true;
                                //     }
                                // }
                                // isWater = true;
                                break;
                            }
                            if (grassMaterialKeywords.Contains(materialName))
                            {
                                // isGrass = true;
                                tileType = TileType.Grass;
                                break;
                            }
                            if (walkableData.walkable.Contains(materialName))
                            {
                                tileType = TileType.Walkable;
                                break;
                            }
                        }

                    }
                }
                if (tileType != TileType.Walkable)
                {
                    // var mapcellsize = gridSize * cellSize;
                    var blockKey = WorldToBlockCoord(cellCenter); //new Vector2Int(Mathf.RoundToInt(cellCenter.x / mapcellsize), Mathf.RoundToInt(cellCenter.z / mapcellsize));
                    MapBlock mapBlock;
                    if (mapCellDatas.mainBlocks.ContainsKey(blockKey))
                    {
                        mapBlock = mapCellDatas.mainBlocks[blockKey];
                        if(mapBlock.mapCellInfo == null) {
                            mapBlock.mapCellInfo = parentTileInfo;
                        }
                    }
                    else
                    {
                        string prefabAddress = GetPrefabAddress(child);
                        mapBlock = new MapBlock
                        {
                            blockName = chunkName,
                            localPosition = localPos,
                            prefabAddress = prefabAddress,
                            tiles = new Dictionary<Vector2Int, MapCellTileData>()
                        };
                        mapBlock.mapCellInfo = parentTileInfo;
                        mapCellDatas.mainBlocks.Add(blockKey, mapBlock);
                    }
                    var tile =  new MapCellTileData
                    {
                        coord = new Vector2Int(x, z),
                        surfaceY = surfaceY,
                        tileType = tileType
                    };
                    tile.IsBattle = isBattle;
                    tile.info = tileInfo;
                    mapBlock.tiles[new Vector2Int(x, z)] = tile;
                }
            }
        }
        return true;
    }
    public static Vector2Int WorldToBlockCoord(Vector3 worldPos) {
        var mapcellsize = gridSize * cellSize;
        return new Vector2Int(Mathf.RoundToInt(worldPos.x / mapcellsize), Mathf.RoundToInt(worldPos.z / mapcellsize));
    }
    public static string GetPrefabAddress(Transform child) {
        string prefabAddress = "";
        GameObject prefabInstance = PrefabUtility.GetNearestPrefabInstanceRoot(child.gameObject);
        if (prefabInstance != null)
        {
            prefabAddress = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(prefabInstance);
            bool matched = false;
            foreach (var name in prefabTypeNames)
            {
                if (prefabAddress.Contains(name))
                {
                    string fileName = Path.GetFileNameWithoutExtension(prefabAddress);
                    prefabAddress = $"{name}_{fileName}";
                    matched = true;
                    break;
                }
            }
            if (!matched)
                prefabAddress = Path.GetFileNameWithoutExtension(prefabAddress);
        }
        return prefabAddress;
    }
    public static void SaveAndLoadWithTiming(MapDataPack mapDatas, string outputBytePath)
    {
        Stopwatch stopwatch = new Stopwatch();

        // **序列化**
        stopwatch.Start();
        byte[] bytes = MessagePackSerializer.Serialize(mapDatas);
        stopwatch.Stop();
        UnityEngine.Debug.Log($"[MessagePack] 序列化耗时: {stopwatch.ElapsedMilliseconds} ms (大小: {bytes.Length / 1024f:F2} KB)");

        // **写入文件**
        stopwatch.Restart();
        File.WriteAllBytes(outputBytePath, bytes);
        stopwatch.Stop();
        UnityEngine.Debug.Log($"[MessagePack] 写入文件耗时: {stopwatch.ElapsedMilliseconds} ms");

        AssetDatabase.Refresh();

        // **读取文件**
        stopwatch.Restart();
        bytes = File.ReadAllBytes(outputBytePath);
        stopwatch.Stop();
        UnityEngine.Debug.Log($"[MessagePack] 读取文件耗时: {stopwatch.ElapsedMilliseconds} ms");

        // **反序列化**
        stopwatch.Restart();
        MapDataPack mapData = MessagePackSerializer.Deserialize<MapDataPack>(bytes);
        stopwatch.Stop();
        UnityEngine.Debug.Log($"[MessagePack] 反序列化耗时: {stopwatch.ElapsedMilliseconds} ms");

        // **总大小 & 数据验证**
        UnityEngine.Debug.Log($"✅ 完成 MessagePack 测试，总大小: {bytes.Length / 1024f:F2} KB，块数量: {mapDatas.mainBlocks.Count}");
    }
}
#endif
