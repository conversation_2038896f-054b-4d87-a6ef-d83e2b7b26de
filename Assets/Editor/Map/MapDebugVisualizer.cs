#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
using UnityEngine.SceneManagement;
using System.Linq;

public static class MapDebugVisualizer
{
    private const int gridSize = 32;
    private const float cellSize = 1f;
    private const float cubeHeight = 0.2f;

    // 你的水面材质关键词
    // private static readonly string[] waterMaterialKeywords = new[] {
    //     // "sea_rock", "sea_rock_m"
    //     "pond_on", "pond_edge", "pond_on_edge", "sea_un", "sea_on", "sea_rock", "sea_line", "sea_rock_m", "river_r"
    // }; "pond_line",
    //, "sea_rock", "sea_rock_m"
    private static readonly string[] waterMaterialKeywords = new[] {
        "pond_on", "pond_edge", "sea_line02", "pond_on_edge", "sea_un", "sea_on", "sea_line", "river_r", "river"
    };
        private static readonly string[] isOnWaterMaterialKeywords = new[] {
            "sea_un", "sea_on", "river_r", "river", "pond_on", "pond_edge", "pond_on_edge"
        };

    private static readonly string[] grassMaterialKeywords = new[] {
            "egrass"
    }; //草地动画
    // private static readonly string[] grassMaterialKeywords = new[] {
    //         "egrass"
    // }; //沙滩，要有脚印

    // private static readonly string[] notwalkableMaterialKeywords = new[] {
    //         "wall01_g", "tree01_re", "tree01gs", "tree01_un", "fence_a", "f_kage", "cl_bg09", "sea_rock", "sea_rock_m", "kbc_rd08", "kbc_wf03"
    // };

    [MenuItem("GameObject/地图/可视化地图格子（包含水面检测）", false, 10)]
    public static void VisualizeGridWithWaterDetection(MenuCommand command)
    {
        GameObject root = command.context as GameObject;
        if (root == null)
        {
            Debug.LogError("❌ 请右键点击一个地图块 GameObject！");
            return;
        }

        var mapName = MaterialWalkableEditor.GetMapTypeName(root, 0);
        if(string.IsNullOrEmpty(mapName)) {
            EditorUtility.DisplayDialog("错误", "❌ 选中的 GameObject 不在任何已知地图中", "确定");
            return;
        }
        MaterialWalkableData walkableData = MaterialWalkableEditor.LoadData(mapName);

        // 清除旧的调试格子
        var existing = root.transform.Find("__GridDebug");
        if (existing != null)
        {
            Object.DestroyImmediate(existing.gameObject);
        }

        GameObject gridParent = new GameObject("__GridDebug");
        gridParent.transform.SetParent(root.transform);
        gridParent.transform.localPosition = Vector3.zero;

        Vector3 origin = root.transform.position - new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize);
        // Vector3 origin = WaterMapChecker.WaterWordOrigin(root.transform.position, new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize));

        for (int x = 0; x < gridSize; x++)
        {
            for (int z = 0; z < gridSize; z++)
            {
                Vector3 cellCenter = origin + new Vector3((x + 0.5f) * cellSize, 50f, (z + 0.5f) * cellSize); // 从高处往下射线
                Debug.Log($"cellCenter: {cellCenter}");
                // Vector3 cellCenter = WaterMapChecker.WaterWordOrigin(origin,  new Vector3((x + 0.5f) * cellSize, 50f, (z + 0.5f) * cellSize));
                bool isWater = false;
                bool isWaterEdge = false;
                bool isGrass = false;
                bool isNotWalkable = false;
                bool IsBattle = false;
                MapCellInfo tileInfo = null;
                string materialName = "(null)";
                string objectName = "(none)";

                // Ray ray = new Ray(cellCenter, Vector3.down);
                float maxDistance = 100f;

                // bool hitFound = false;
                // RaycastHit closestHit = new RaycastHit();
                // float closestDistance = float.MaxValue;
                var fff = 0.3f;
                // 检查当前中心 + 8 个邻居方向（共 9 个）
                Vector3[] directions = new Vector3[]
                {
                    Vector3.zero,                                   // 自身
                    // new Vector3(-fff * cellSize, 0, 0),            // 左
                    // new Vector3(fff * cellSize, 0, 0),             // 右
                    // new Vector3(0, 0, -fff * cellSize),            // 下
                    // new Vector3(0, 0, fff * cellSize),             // 上
                    // new Vector3(-fff * cellSize, 0, -fff * cellSize), // 左下
                    // new Vector3(fff * cellSize, 0, -fff * cellSize),  // 右下
                    // new Vector3(-fff * cellSize, 0, fff * cellSize),  // 左上
                    // new Vector3(fff * cellSize, 0, fff * cellSize),   // 右上
                };

                foreach (var offset in directions)
                {
                    Vector3 probePoint = cellCenter + offset;
                    Ray ray = new Ray(probePoint, Vector3.down);

                    bool hitFound = false;
                    RaycastHit closestHit = new RaycastHit();
                    float closestDistance = float.MaxValue;

                    foreach (var collider in root.GetComponentsInChildren<Collider>(true))
                    {
                        if (collider.Raycast(ray, out RaycastHit hit, maxDistance))
                        {
                            float distance = hit.distance;
                            if (distance < closestDistance)
                            {
                                closestDistance = distance;
                                closestHit = hit;
                                hitFound = true;
                            }
                        }
                    }

                    if (hitFound)
                    {
                        objectName = closestHit.collider.name;
                        var item = closestHit.collider.GetComponent<MapTilePrefabComponent>();
                        if(item != null) {
                            var parentName = item.gameObject.transform.parent.name;
                            tileInfo = new MapCellInfo
                            {
                                Bgm = item.Bgm,
                                RegionId = item.RegionId,
                                AreaId = item.AreaId,
                                BattleBgm = item.BattleBgm,
                                // IsBattle = item.IsBattle,
                                TileType = item.TileType
                            };
                            IsBattle = item.IsBattle;
                        }
                        // if (item.gameObject.transform.parent == topLevel)
                        // {
                        //     parentName = typeof(MapLoader).Name;
                        // }
                        // prefabMapTileInfos[parentName + "_" + item.gameObject.name] = new PrefabMapTileInfo
                        // {
                        //     Bgm = item.Bgm,
                        //     RegionId = item.RegionId,
                        //     AreaId = item.AreaId,
                        //     BattleBgm = item.BattleBgm,
                        //     IsBattle = item.IsBattle,
                        //     TileType = item.TileType
                        // };
                        // Dictionary<string, PrefabMapTileInfo> prefabMapTileInfos = new();
                        // foreach (var item in child.GetComponentsInChildren<MapTilePrefabComponent>())
                        // {
                        //     var parentName = item.gameObject.transform.parent.name;
                        //     if (item.gameObject.transform.parent == topLevel)
                        //     {
                        //         parentName = typeof(MapLoader).Name;
                        //     }
                        //     prefabMapTileInfos[parentName + "_" + item.gameObject.name] = new PrefabMapTileInfo
                        //     {
                        //         Bgm = item.Bgm,
                        //         RegionId = item.RegionId,
                        //         AreaId = item.AreaId,
                        //         BattleBgm = item.BattleBgm,
                        //         IsBattle = item.IsBattle,
                        //         TileType = item.TileType
                        //     };
                        // }
                        var renderer = closestHit.collider.GetComponent<Renderer>();
                        if (renderer != null && renderer.sharedMaterial != null)
                        {
                            materialName = renderer.sharedMaterial.name.ToLowerInvariant();
                            if (waterMaterialKeywords.Contains(materialName)) {
                                if(!isWaterEdge) {
                                    bool onWater = false;
                                    if(isOnWaterMaterialKeywords.Contains(materialName)) {
                                        onWater = true;
                                    }
                                    // foreach (var keywordd in isOnWaterMaterialKeywords)
                                    // {
                                    //     if (materialName.Contains(keywordd))
                                    //     {
                                    //         onWater = true;
                                    //         break;
                                    //     }
                                    // }
                                    if(!onWater) {
                                        isWaterEdge = true;
                                    }
                                }
                                isWater = true;
                                break;
                            }
                            if(grassMaterialKeywords.Contains(materialName)) {
                                isGrass = true;
                                break;
                                // 如果是草地材质，可以在这里处理
                                // Debug.Log($"Grass material found: {materialName}");
                            }
                            if(walkableData.nonWalkable.Contains(materialName)) {
                                isNotWalkable = true;
                                break;
                            }
                            // foreach (var keyword in waterMaterialKeywords)
                            // {
                            //     if (materialName.Contains(keyword))
                            //     {
                            //         if(!isWaterEdge) {
                            //             bool onWater = false;
                            //             if(isOnWaterMaterialKeywords.Contains(materialName)) {
                            //                 onWater = true;
                            //             }
                            //             // foreach (var keywordd in isOnWaterMaterialKeywords)
                            //             // {
                            //             //     if (materialName.Contains(keywordd))
                            //             //     {
                            //             //         onWater = true;
                            //             //         break;
                            //             //     }
                            //             // }
                            //             if(!onWater) {
                            //                 isWaterEdge = true;
                            //             }
                            //         }
                            //         isWater = true;
                            //         break;
                            //     }
                            // }
                        }
                        
                    }

                    // if (isWater)
                    //     break; // 一旦发现是水，就不需要继续检测其他方向了
                }

            

                // 遍历 root 下所有子对象，找到最近的碰撞
                // foreach (var collider in root.GetComponentsInChildren<Collider>(true))
                // {
                //     if (collider.Raycast(ray, out RaycastHit hit, maxDistance))
                //     {
                //         float distance = hit.distance;
                //         if (distance < closestDistance)
                //         {
                //             closestDistance = distance;
                //             closestHit = hit;
                //             hitFound = true;
                //         }
                //     }
                // }

                // if (hitFound)
                // {
                //     objectName = closestHit.collider.name;

                //     var renderer = closestHit.collider.GetComponent<Renderer>();
                //     if (renderer != null && renderer.sharedMaterial != null)
                //     {
                //         materialName = renderer.sharedMaterial.name.ToLowerInvariant();
                //         foreach (var keyword in waterMaterialKeywords)
                //         {
                //             if (materialName.Contains(keyword))
                //             {
                //                 isWater = true;
                //                 break;
                //             }
                //         }
                //     }
                // }
                // Scene scene = root.scene;

                // if (ScenePhysics.Raycast(scene, cellCenter, Vector3.down, out RaycastHit hit, 100f))
                // {
                //     objectName = hit.collider.name;

                //     var renderer = hit.collider.GetComponent<Renderer>();
                //     if (renderer != null && renderer.sharedMaterial != null)
                //     {
                //         materialName = renderer.sharedMaterial.name.ToLowerInvariant();
                //         foreach (var keyword in waterMaterialKeywords)
                //         {
                //             if (materialName.Contains(keyword))
                //             {
                //                 isWater = true;
                //                 break;
                //             }
                //         }
                //     }
                // }

                Debug.Log($"[({x},{z})] Hit Object: {objectName}, Material: {materialName}, IsWater: {isWater}");

                // 创建 cube
                GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
                cube.transform.SetParent(gridParent.transform);
                cube.transform.position = new Vector3(cellCenter.x, 5f, cellCenter.z);
                cube.transform.localScale = new Vector3(1f, cubeHeight, 1f);

                // 设置材质颜色
                var cubeRenderer = cube.GetComponent<Renderer>();
                var urpShader = Shader.Find("Universal Render Pipeline/Lit");

                if (urpShader == null)
                {
                    Debug.LogError("❌ 未找到 URP Shader，确认你是否已启用 URP！");
                }
                else
                {
                    var mat = new Material(urpShader);
                    if(tileInfo != null) {
                        if(IsBattle) {
                            mat.color = Color.red;
                        }
                    }
                    mat.color = isWater ? Color.cyan : Color.white;
                    if(isWaterEdge && isWater) {
                        mat.color = Color.yellow;
                    }
                    if(isGrass) {
                        mat.color = Color.green;
                    }
                    if(isNotWalkable) {
                        mat.color = Color.black;
                    }
                    cubeRenderer.sharedMaterial = mat;
                }
            }
        }

        Debug.Log($"✅ 完成格子渲染和水面检测: {root.name}");
    }
}
#endif
