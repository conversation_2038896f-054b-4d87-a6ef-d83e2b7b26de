// #if UNITY_EDITOR
// using UnityEngine;
// using UnityEditor;
// using UnityEditor.SceneManagement;
// using UnityEditor.Experimental.SceneManagement;
// using System.Collections.Generic;
// using System.IO;
// using System;
// using Newtonsoft.Json;
// using System.Linq;

// public static class WaterPrefabDataEditor
// {
//     private const int gridSize = 32;
//     private const float cellSize = 1f;
//     private static readonly string[] waterMaterialKeywords = new[] {
//         "pond_on", "pond_edge", "sea_line02", "pond_line", "pond_on_edge", "sea_un", "sea_on", "sea_rock", "sea_line", "sea_rock_m", "river_r", "river"
//     };
//     private static readonly string[] isOnWaterMaterialKeywords = new[] {
//         "sea_un", "sea_on", "river_r", "river", "pond_on"
//     };


//     [MenuItem("Tools/Map/导出水面和地形数据")]
//     public static void ExportAllMapData()
//     {
//         var prefabStage = PrefabStageUtility.GetCurrentPrefabStage();
//         GameObject prefabRoot = PrefabStageUtility.GetCurrentPrefabStage()?.prefabContentsRoot;
//         if (prefabRoot == null)
//         {
//             EditorUtility.DisplayDialog("提示", "请在Prefab模式下打开一个Prefab进行导出。", "确定");
//             return;
//         }

//         string folderPath = Application.dataPath + "/AssetRaw/Map/Json";
//         if (!Directory.Exists(folderPath)) Directory.CreateDirectory(folderPath);

//         string outputJsonPath = Path.Combine(folderPath, prefabRoot.name + "_TestWater.json");

//         MapData mapdata = new MapData();

//         List<string> prefabTypeNames = new() { "HeartGold", "Platinum", "White2" };

//         // 水域数据
//         var waterMapData = new WaterMapData
//         {
//             gridSize = gridSize,
//             cellSize = cellSize,
//             blocks = new List<WaterMapBlock>()
//         };
//         foreach (Transform topLevel in prefabRoot.transform)
//         {
//             var exportList = new List<PrefabData>();
//             var transmitMapNames = new HashSet<string>();
            
//             foreach (Transform child in topLevel.transform)
//             {
//                 if (child == topLevel) continue;
//                 string prefabName = child.name;
//                 Vector3 pos = child.localPosition;
//                 pos.y += (Mathf.Abs(child.parent.localPosition.y) < 0.1f ? 0 : child.parent.localPosition.y);
//                 Vector3 rotation = child.localRotation.eulerAngles;
//                 Vector3 scale = child.localScale;

//                 string prefabAddress = "";
//                 GameObject prefabInstance = PrefabUtility.GetNearestPrefabInstanceRoot(child.gameObject);
//                 if (prefabInstance != null)
//                 {
//                     prefabAddress = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(prefabInstance);
//                     bool matched = false;
//                     foreach (var name in prefabTypeNames)
//                     {
//                         if (prefabAddress.Contains(name))
//                         {
//                             string fileName = Path.GetFileNameWithoutExtension(prefabAddress);
//                             prefabAddress = $"{name}_{fileName}";
//                             matched = true;
//                             break;
//                         }
//                     }
//                     if (!matched)
//                         prefabAddress = Path.GetFileNameWithoutExtension(prefabAddress);
//                 }

//                 if (topLevel.name.EndsWith("Main"))
//                 {
//                     string chunkName = child.name;
//                     Vector3 localPos = child.localPosition;
//                     Vector3 origin = child.transform.position - new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize);
//                     // Vector3 origin = WaterMapChecker.WaterWordOrigin(root.transform.position, new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize));
//                     List<WaterCell> waterCells = new();
//                     for (int x = 0; x < gridSize; x++)
//                     {
//                         for (int z = 0; z < gridSize; z++)
//                         {
//                             Vector3 cellCenter = origin + new Vector3((x + 0.5f) * cellSize, 50f, (z + 0.5f) * cellSize); // 从高处往下射线
//                             Debug.Log($"cellCenter: {cellCenter}");
//                             // Vector3 cellCenter = WaterMapChecker.WaterWordOrigin(origin,  new Vector3((x + 0.5f) * cellSize, 50f, (z + 0.5f) * cellSize));
//                             bool isWater = false;
//                             bool isWaterEdge = false;
//                             string materialName = "(null)";
//                             string objectName = "(none)";

//                             // Ray ray = new Ray(cellCenter, Vector3.down);
//                             float maxDistance = 100f;

//                             // bool hitFound = false;
//                             // RaycastHit closestHit = new RaycastHit();
//                             // float closestDistance = float.MaxValue;
//                             var fff = 0.3f;
//                             // 检查当前中心 + 8 个邻居方向（共 9 个）
//                             Vector3[] directions = new Vector3[]
//                             {
//                                 Vector3.zero,                                   // 自身
//                                 // new Vector3(-fff * cellSize, 0, 0),            // 左
//                                 // new Vector3(fff * cellSize, 0, 0),             // 右
//                                 // new Vector3(0, 0, -fff * cellSize),            // 下
//                                 // new Vector3(0, 0, fff * cellSize),             // 上
//                                 // new Vector3(-fff * cellSize, 0, -fff * cellSize), // 左下
//                                 // new Vector3(fff * cellSize, 0, -fff * cellSize),  // 右下
//                                 // new Vector3(-fff * cellSize, 0, fff * cellSize),  // 左上
//                                 // new Vector3(fff * cellSize, 0, fff * cellSize),   // 右上
//                             };
//                             var surfaceY = 0f;
//                             foreach (var offset in directions)
//                             {
//                                 if (isWaterEdge && isWater)
//                                 {
//                                     break;
//                                 }
//                                 Vector3 probePoint = cellCenter + offset;
//                                 Ray ray = new Ray(probePoint, Vector3.down);

//                                 bool hitFound = false;
//                                 RaycastHit closestHit = new RaycastHit();
//                                 float closestDistance = float.MaxValue;

//                                 foreach (var collider in child.GetComponentsInChildren<Collider>(true))
//                                 {
//                                     if (collider.Raycast(ray, out RaycastHit hit, maxDistance))
//                                     {
//                                         float distance = hit.distance;
//                                         if (distance < closestDistance)
//                                         {
//                                             closestDistance = distance;
//                                             closestHit = hit;
//                                             hitFound = true;
//                                         }
//                                     }
//                                 }

//                                 if (hitFound)
//                                 {
//                                     objectName = closestHit.collider.name;

//                                     var renderer = closestHit.collider.GetComponent<Renderer>();
//                                     if (renderer != null && renderer.sharedMaterial != null)
//                                     {
//                                         materialName = renderer.sharedMaterial.name.ToLowerInvariant();
//                                         foreach (var keyword in waterMaterialKeywords)
//                                         {
//                                             if (materialName.Contains(keyword))
//                                             {
//                                                 if (!isWaterEdge)
//                                                 {
//                                                     bool onWater = false;
//                                                     if (isOnWaterMaterialKeywords.Contains(materialName))
//                                                     {
//                                                         onWater = true;
//                                                     }
//                                                     // foreach (var keywordd in isOnWaterMaterialKeywords)
//                                                     // {
//                                                     //     if (materialName.Contains(keywordd))
//                                                     //     {
//                                                     //         onWater = true;
//                                                     //         break;
//                                                     //     }
//                                                     // }
//                                                     if (!onWater)
//                                                     {
//                                                         isWaterEdge = true;
//                                                     }
//                                                 }
//                                                 surfaceY = closestHit.point.y;
//                                                 isWater = true;
//                                                 break;
//                                             }
//                                         }
//                                     }

//                                 }

//                                 // if (isWater)
//                                 //     break; // 一旦发现是水，就不需要继续检测其他方向了
//                             }

//                             if (isWater)
//                             {
//                                 var isScene = prefabStage.scene.GetRootGameObjects()[0].gameObject.name == "Prefab Mode in Context";
//                                 isScene = false;
//                                 // var isScene = prefabStage.scene.buildIndex != -1;

//                                 waterCells.Add(new WaterCell
//                                 {
//                                     // coord = isScene ? new Vector2Int(x, z) : new Vector2Int(gridSize - x - 1, z), //在prefab上是这样的
//                                     coord = new Vector2Int(x, z), //在scene上是这样的
//                                     surfaceY = surfaceY,   // ✅ 用正确的 Hit 信息
//                                     isWaterEdge = isWaterEdge
//                                 });
//                             }



//                             // 遍历 root 下所有子对象，找到最近的碰撞
//                             // foreach (var collider in root.GetComponentsInChildren<Collider>(true))
//                             // {
//                             //     if (collider.Raycast(ray, out RaycastHit hit, maxDistance))
//                             //     {
//                             //         float distance = hit.distance;
//                             //         if (distance < closestDistance)
//                             //         {
//                             //             closestDistance = distance;
//                             //             closestHit = hit;
//                             //             hitFound = true;
//                             //         }
//                             //     }
//                             // }

//                             // if (hitFound)
//                             // {
//                             //     objectName = closestHit.collider.name;

//                             //     var renderer = closestHit.collider.GetComponent<Renderer>();
//                             //     if (renderer != null && renderer.sharedMaterial != null)
//                             //     {
//                             //         materialName = renderer.sharedMaterial.name.ToLowerInvariant();
//                             //         foreach (var keyword in waterMaterialKeywords)
//                             //         {
//                             //             if (materialName.Contains(keyword))
//                             //             {
//                             //                 isWater = true;
//                             //                 break;
//                             //             }
//                             //         }
//                             //     }
//                             // }
//                             // Scene scene = root.scene;

//                             // if (ScenePhysics.Raycast(scene, cellCenter, Vector3.down, out RaycastHit hit, 100f))
//                             // {
//                             //     objectName = hit.collider.name;

//                             //     var renderer = hit.collider.GetComponent<Renderer>();
//                             //     if (renderer != null && renderer.sharedMaterial != null)
//                             //     {
//                             //         materialName = renderer.sharedMaterial.name.ToLowerInvariant();
//                             //         foreach (var keyword in waterMaterialKeywords)
//                             //         {
//                             //             if (materialName.Contains(keyword))
//                             //             {
//                             //                 isWater = true;
//                             //                 break;
//                             //             }
//                             //         }
//                             //     }
//                             // }

//                             // Debug.Log($"[({x},{z})] Hit Object: {objectName}, Material: {materialName}, IsWater: {isWater}");

//                         }
//                     }
//                     if (waterCells.Count > 0)
//                     {
//                         waterMapData.blocks.Add(new WaterMapBlock
//                         {
//                             blockName = chunkName,
//                             localPosition = localPos,
//                             waterCells = waterCells.ToArray()
//                         });
//                     }



//                     // //水数据
//                     // string chunkName = child.name;
//                     // Vector3 localPos = child.localPosition;
//                     // Vector3 chunkOrigin = child.parent.position + child.localPosition - new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize);
//                     // // Vector3 chunkOrigin = child.position - new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize);
//                     // List<WaterCell> waterCells = new();

//                     // for (int x = 0; x < gridSize; x++)
//                     // {
//                     //     for (int z = 0; z < gridSize; z++)
//                     //     {
//                     //         Vector3 cellCenter = chunkOrigin + new Vector3((x + 0.5f) * cellSize, 50f, (z + 0.5f) * cellSize);
//                     //         // 坐标偏移，确保 (0,0) 是地图块左上角
//                     //         // Vector3 localOffset = new Vector3(
//                     //         //     (x + 0.5f - gridSize / 2f) * cellSize,
//                     //         //     50f,
//                     //         //     (z + 0.5f - gridSize / 2f) * cellSize
//                     //         // );

//                     //         // // 把本地位置变成世界坐标，适配 Prefab / Scene 模式
//                     //         // Vector3 cellCenter = child.transform.TransformPoint(localOffset);


//                     //         Ray ray = new Ray(cellCenter, Vector3.down);
//                     //         float maxDistance = 100f;

//                     //         bool hitFound = false;
//                     //         RaycastHit closestHit = new RaycastHit();
//                     //         float closestDistance = float.MaxValue;
//                     //         var fff = 0.3f;
//                     //         // 检查当前中心 + 8 个邻居方向（共 9 个）
//                     //         Vector3[] directions = new Vector3[]
//                     //         {
//                     //             Vector3.zero,                                   // 自身
//                     //             new Vector3(-fff * cellSize, 0, 0),            // 左
//                     //             new Vector3(fff * cellSize, 0, 0),             // 右
//                     //             new Vector3(0, 0, -fff * cellSize),            // 下
//                     //             new Vector3(0, 0, fff * cellSize),             // 上
//                     //             new Vector3(-fff * cellSize, 0, -fff * cellSize), // 左下
//                     //             new Vector3(fff * cellSize, 0, -fff * cellSize),  // 右下
//                     //             new Vector3(-fff * cellSize, 0, fff * cellSize),  // 左上
//                     //             new Vector3(fff * cellSize, 0, fff * cellSize),   // 右上
//                     //         };

//                     //         bool isWaterEdge = false;
//                     //         bool isWater = false;
//                     //         foreach (var offset in directions)
//                     //         {
//                     //             // 遍历 root 下所有子对象，找到最近的碰撞
//                     //             foreach (var collider in child.GetComponentsInChildren<Collider>(true))
//                     //             {
//                     //                 if (collider.Raycast(ray, out RaycastHit hit, maxDistance))
//                     //                 {
//                     //                     float distance = hit.distance;
//                     //                     if (distance < closestDistance)
//                     //                     {
//                     //                         closestDistance = distance;
//                     //                         closestHit = hit;
//                     //                         hitFound = true;
//                     //                     }
//                     //                 }
//                     //             }

//                     //             if (hitFound)
//                     //             {
//                     //                 var renderer = closestHit.collider.GetComponent<Renderer>();
//                     //                 if (renderer != null && renderer.sharedMaterial != null)
//                     //                 {
//                     //                     string materialName = renderer.sharedMaterial.name.ToLowerInvariant();
//                     //                     foreach (var keyword in waterMaterialKeywords)
//                     //                     {
//                     //                         if (materialName.Contains(keyword))
//                     //                         {
//                     //                             if(!isWaterEdge) {
//                     //                                 bool onWater = false;
//                     //                                 if(isOnWaterMaterialKeywords.Contains(materialName)) {
//                     //                                     onWater = true;
//                     //                                 }
//                     //                                 // foreach (var keywordd in isOnWaterMaterialKeywords)
//                     //                                 // {
//                     //                                 //     if (materialName.Contains(keywordd))
//                     //                                 //     {
//                     //                                 //         onWater = true;
//                     //                                 //         break;
//                     //                                 //     }
//                     //                                 // }
//                     //                                 if(!onWater) {
//                     //                                     isWaterEdge = true;
//                     //                                 }
//                     //                             }
//                     //                             isWater = true;
//                     //                             break;
//                     //                         }
//                     //                     }
//                     //                     // foreach (var keyword in isOnWaterMaterialKeywords)
//                     //                     // {
//                     //                     //     if (matName.Contains(keyword))
//                     //                     //     {
//                     //                     //         isWaterEdge = false;
//                     //                     //         break;
//                     //                     //     }
//                     //                     // }
//                     //                     // if(!isWaterEdge) {
//                     //                     //     bool onWater = false;
//                     //                     //     if (isOnWaterMaterialKeywords.Contains(matName))
//                     //                     //     {
//                     //                     //         onWater = true;
//                     //                     //     }
//                     //                     //     // foreach (var keywordd in isOnWaterMaterialKeywords)
//                     //                     //     // {

//                     //                     //     // }
//                     //                     //     if(!onWater) {
//                     //                     //         isWaterEdge = true;
//                     //                     //     }
//                     //                     // }
//                     //                     // if (waterMaterialKeywords.Contains(matName))
//                     //                     // {
//                     //                     //     isWater = true;
//                     //                     //     break;
//                     //                     // }
//                     //                     // foreach (var keyword in waterMaterialKeywords)
//                     //                     // {
//                     //                     //     if (waterMaterialKeywords.Contains(matName))
//                     //                     //     {
//                     //                     //         var isScene = prefabStage.scene.GetRootGameObjects()[0].gameObject.name == "Prefab Mode in Context";
//                     //                     //         // var isScene = prefabStage.scene.buildIndex != -1;

//                     //                     //         waterCells.Add(new WaterCell
//                     //                     //         {
//                     //                     //             coord = isScene ? new Vector2Int(x, z) : new Vector2Int(gridSize-x-1, z), //在prefab上是这样的
//                     //                     //             // coord = new Vector2Int(x, z), //在scene上是这样的
//                     //                     //             surfaceY = closestHit.point.y,   // ✅ 用正确的 Hit 信息
//                     //                     //             isWaterEdge = isWaterEdge
//                     //                     //         });
//                     //                     //         break;
//                     //                     //     }
//                     //                     // }
//                     //                 }
//                     //             }
//                     //         }
//                     //         if(isWater) {
//                     //             var isScene = prefabStage.scene.GetRootGameObjects()[0].gameObject.name == "Prefab Mode in Context";
//                     //             // var isScene = prefabStage.scene.buildIndex != -1;

//                     //             waterCells.Add(new WaterCell
//                     //             {
//                     //                 coord = isScene ? new Vector2Int(x, z) : new Vector2Int(gridSize-x-1, z), //在prefab上是这样的
//                     //                 // coord = new Vector2Int(x, z), //在scene上是这样的
//                     //                 surfaceY = closestHit.point.y,   // ✅ 用正确的 Hit 信息
//                     //                 isWaterEdge = isWaterEdge
//                     //             });
//                     //         }

//                     //         // if (Physics.Raycast(cellCenter, Vector3.down, out RaycastHit hit, 100f))
//                     //         // {
//                     //         //     var renderer = hit.collider.GetComponent<Renderer>();
//                     //         //     if (renderer != null && renderer.sharedMaterial != null)
//                     //         //     {
//                     //         //         string matName = renderer.sharedMaterial.name.ToLowerInvariant();
//                     //         //         foreach (var keyword in waterMaterialKeywords)
//                     //         //         {
//                     //         //             if (matName.Contains(keyword))
//                     //         //             {
//                     //         //                 waterCells.Add(new WaterCell
//                     //         //                 {
//                     //         //                     coord = new Vector2Int(x, z),
//                     //         //                     surfaceY = hit.point.y
//                     //         //                 });
//                     //         //                 break;
//                     //         //             }
//                     //         //         }
//                     //         //     }
//                     //         // }
//                     //     }
//                     // }

//                     // if (waterCells.Count > 0)
//                     // {
//                     //     waterMapData.blocks.Add(new WaterMapBlock
//                     //     {
//                     //         blockName = chunkName,
//                     //         localPosition = localPos,
//                     //         waterCells = waterCells.ToArray()
//                     //     });
//                     // }
//                 }

//             }

//             if (exportList.Count > 0)
//             {

//                 //     configFileNames.Add($"Map_{mapName}");
//                 // configFileNames.Add($"Map_{mapName}Building");
//                 // configFileNames.Add($"Map_{mapName}Transfer");
//                 // configFileNames.Add($"Map_{mapName}Env");
//                 // configFileNames.Add($"Map_{mapName}EnvOutBuilding");
//                 // configFileNames.Add($"Map_{mapName}MultipleOut");
//                 // configFileNames.Add($"Map_{mapName}Interior");
//                 if (topLevel.name.EndsWith("Main"))
//                 {
//                     mapdata.mainMap = exportList;
//                 }
//                 else if (topLevel.name.EndsWith("MainBuilding"))
//                 {
//                     mapdata.buildingMap = exportList;
//                 }
//                 else if (topLevel.name.EndsWith("Transfer"))
//                 {
//                     mapdata.transferMap = exportList;
//                 }
//                 else if (topLevel.name.EndsWith("Env"))
//                 {
//                     mapdata.envMap = exportList;
//                 }
//                 else if (topLevel.name.EndsWith("EnvOutBuilding"))
//                 {
//                     mapdata.envOutBuildingMap = exportList;
//                 }
//                 else if (topLevel.name.EndsWith("MultipleOut"))
//                 {
//                     mapdata.multipleOutMap = exportList;
//                 }
//                 else if (topLevel.name.EndsWith("Interior"))
//                 {
//                     mapdata.interiorMap = exportList;
//                 }
//                 else if (topLevel.name.EndsWith("Other"))
//                 {
//                     mapdata.otherMap = exportList;
//                 }
//                 else if (topLevel.name.EndsWith("Npc"))
//                 {
//                     //
//                 }
//                 else if (topLevel.name.EndsWith("EventObjects"))
//                 {
//                     //
//                 }
//                 else
//                 {
//                     EditorUtility.DisplayDialog("导出错误", $"未知的顶层名称: {topLevel.name}", "确定");
//                     return;
//                 }
//                 // combinedData.prefabData[topLevel.name] = exportList;
//             }
//         }
        
//         mapdata.waterMap = waterMapData;

//         var settings = new JsonSerializerSettings
//         {
//             Converters = new[] {
//                 new Vec3Conv(),
//                 //new StringEnumConverter(),
//             },
//         };
//         // string json = JsonUtility.ToJson(mapdata, true);
//         // File.WriteAllText(outputJsonPath, json);
//         // AssetDatabase.Refresh();

//         // EditorUtility.DisplayDialog("✅ 导出完成", $"已保存至:\n{outputJsonPath}", "好");
//         // var myObjectINeedToSerialize = new Vector3(1, 2, 3);

//         string json = JsonConvert.SerializeObject(mapdata, settings);
//         File.WriteAllText(outputJsonPath, json);
//         AssetDatabase.Refresh();

//         EditorUtility.DisplayDialog("✅ 导出完成", $"已保存至:\n{outputJsonPath}", "好");
//     }


// }
// #endif
