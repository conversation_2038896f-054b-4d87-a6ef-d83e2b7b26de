using System.Linq;
using Newtonsoft.Json;
using UnityEngine;

public class InstanceMapConfig {
    public static InstanceMapInfo MewtwoAndMew() {
        var name = "MewtwoAndMew";
        var unlockItemName = InstanceMapUnlockItem.mewtwomailspecial;
        // var mainLandInfo = MainMapInfo.Create(MainServer.MainLandType.MainLandHeartGold);
        var mapdata = GetMapData(name);
        var envInfo = new MapEnvInfo {
            MapWeather = WeatherEnumType.RainDance,
            timeHour = 10
        };
        var mapInfo = new InstanceMapInfo(name, mapdata, unlockItemName, "20010", envInfo);
        mapInfo.NameLocalized = "超梦和梦幻";
        return mapInfo;
    }
    private static MapDataPack GetMapData(string instanceMap) {
        return MapDataPack.LoadMapData("Map_" + instanceMap);
    }
    // private static string GetNavMeshName(string name) {
    //     return "NavMesh-" + name;
    // }
    // private static MapData MergeMainLoad(MapData instaceMapInfo, MapData mainMapInfo) {
    //     mainMapInfo.npcs = instaceMapInfo.npcs;
    //     return instaceMapInfo;
    // }
}