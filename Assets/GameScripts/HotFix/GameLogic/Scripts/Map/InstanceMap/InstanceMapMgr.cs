using System.Collections.Generic;
using System.Linq;

public class InstanceMapMgr {
    public static InstanceMapMgr Share = new InstanceMapMgr();
    private Dictionary<string, InstanceMapInfo> instanceMapInfoMap = new();
    private Dictionary<string, string> unlockItemNameToMapName = new();
    InstanceMapMgr() {
        // instanceMapInfoMap.Add(InstanceMapConfig.MewtwoAndMew().Name, InstanceMapConfig.MewtwoAndMew());
        // foreach (var item in instanceMapInfoMap.Values)
        // {
        //     unlockItemNameToMapName.Add(item.UnlockItemName, item.Name);
        // }
    }
    public static InstanceMapInfo? GetInstanceMapInfo(string name) {
        if(Share.instanceMapInfoMap.TryGetValue(name, out var mapInfo)) {
            return mapInfo;
        }
        return null;
    }
    public List<InstanceMapInfo> GetItemActiveInstanceMapInfos() {
        var mapInfos = new List<InstanceMapInfo>();
        foreach (var item in GameContext.Current.Trainer.Items)
        {
            if(unlockItemNameToMapName.Keys.Contains(item.Key) && !CheckItemExpiredByItemName(item.Key)) {
                mapInfos.Add(instanceMapInfoMap[unlockItemNameToMapName[item.Key]]);
            }
        }
        // mapInfos.Add(InstanceMapConfig.MewtwoAndMew());
        // if(GameContext.Current.Trainer.Items[]) {

        // }
        // mapInfos.Add()
        return mapInfos;
    }
    public bool CheckItemExpiredByItemName(string itemName) {
        if(GameContext.Current.Trainer.Items.TryGetValue(itemName, out var item)) {
            return item.ExpireTs <= CoreContext.Current.CurrentServerTime;
        }
        return true;
    }
    public bool CheckItemExpiredByMapNameId(string mapNameId) {
        for(int i =0; i < unlockItemNameToMapName.Values.Count; i++) {
            if(unlockItemNameToMapName.Values.ElementAt(i) == mapNameId) {
                return CheckItemExpiredByItemName(unlockItemNameToMapName.Keys.ElementAt(i));
            }
        }
        return true;
    }
}