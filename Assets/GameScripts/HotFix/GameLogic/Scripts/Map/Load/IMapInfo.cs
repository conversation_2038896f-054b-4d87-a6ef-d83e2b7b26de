using UnityEngine;

#nullable enable

public interface IMapInfo
{
    // List<string> GetConfigFiles();
    // List<string> GetConfigWaterFiles();
    // string GetMapNpcConfigFileName();
    string GetMapNameId();
    MapDataPack GetMapData();
    MainServer.MainLandType GetMainLandType();
    MapTransmitPointInfoPack? GetInitPosition(string tagName);
    public MapTransmitPointInfoPack? GetDefaultInitPosition();
    public MapEnvInfo? GetEnvInfo();
}
public class MapEnvInfo {
    public WeatherEnumType MapWeather;
    public int timeHour;
}