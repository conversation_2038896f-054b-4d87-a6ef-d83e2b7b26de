using System.Collections.Generic;
using UnityEngine;
#nullable enable
public class InstanceMapInfo : IMapInfo
{
    // private Vector3 _initPosition;
    public string Name { get; private set; }
    public string UnlockItemName { get; private set; }
    public string QuestId { get; private set; }
    public string NameLocalized { get; set; }
    public MapEnvInfo envInfo;
    private MapData _mapData;
    
    public MainServer.TrainerQuest? trainerQuest;
    // private string _navMeshName;
    public InstanceMapInfo(string name, MapData mapData, string unlockItemName, string questId, MapEnvInfo envInfo) {
        Name = name;
        QuestId = questId;
        UnlockItemName = unlockItemName;
        this.envInfo = envInfo;
        _mapData = mapData;
        // _navMeshName = navMeshName;
    }
    public MapTransmitPointInfoPack GetDefaultInitPosition() {
        return GetInitPosition(null);
    }
    public MapTransmitPointInfoPack GetInitPosition(string tagName)
    {
        var mapData = GetMapData();
        if(mapData.initPostions == null || mapData.initPostions.Count == 0) {
            return null;
        }
        if(string.IsNullOrEmpty(tagName)) {
            return mapData.initPostions[0].transmitMapInfo;
        }
        foreach (var item in mapData.initPostions)
        {
            if(item.tagName == tagName) {
                return item.transmitMapInfo;
            }
        }
        return mapData.initPostions[0].transmitMapInfo;
        // switch (_mapType)
        // {
        //     case MapType.HeartGold:
        //         // return new Vector3(672.95f, 1, -384.97f);
        //         if(gen == "gen1") {
        //             return new Vector3(1024.27f, 1, -350.75f);
        //         } else { //if(gen == "gen2")
        //             return new Vector3(672.95f, 1, -384.97f);
        //         }
        //     case MapType.Platinum:
        //         return new Vector3(-1024, 0, 1024);
        //     case MapType.White:
        //         return new Vector3(-1024, 0, 1024);
        //     default:
        //         return new Vector3(-1024, 0, 1024);
        // }
    }

    public MapData GetMapData()
    {
        return _mapData;
    }
    public string GetMapNameId()
    {
        return Name;
    }

    public MapEnvInfo GetEnvInfo()
    {
        return envInfo;
    }
    public MainServer.MainLandType GetMainLandType()
    {
        return MainServer.MainLandType.MainLandNone;
    }
}