using System.Collections.Generic;
using UnityEngine;
using Newtonsoft.Json;
using MainServer;

#nullable enable
public class MainMapInfo: IMapInfo {
    // public enum MapType {
    //     HeartGold,
    //     Platinum,
    //     White,
    // }
    private MapDataPack? _mapData;
    public MainServer.MainLandType mapType { get; private set; }
    public MapTransmitPointInfoPack GetDefaultInitPosition()
    {
        return GetInitPosition(null);
    }
    public MapTransmitPointInfoPack GetInitPosition(string? tagName)
    {
        var mapData = GetMapData();
        if(mapData.initPostions == null || mapData.initPostions.Count == 0) {
            return null;
        }
        if(string.IsNullOrEmpty(tagName)) {
            return mapData.initPostions[0].transmitMapInfo;
        }
        foreach (var item in mapData.initPostions)
        {
            if(item.tagName == tagName) {
                return item.transmitMapInfo;
            }
        }
        return mapData.initPostions[0].transmitMapInfo;
        // switch (_mapType)
        // {
        //     case MapType.HeartGold:
        //         // return new Vector3(672.95f, 1, -384.97f);
        //         if(gen == "gen1") {
        //             return new Vector3(1024.27f, 1, -350.75f);
        //         } else { //if(gen == "gen2")
        //             return new Vector3(672.95f, 1, -384.97f);
        //         }
        //     case MapType.Platinum:
        //         return new Vector3(-1024, 0, 1024);
        //     case MapType.White:
        //         return new Vector3(-1024, 0, 1024);
        //     default:
        //         return new Vector3(-1024, 0, 1024);
        // }
    }
    public static MainMapInfo Create(MainServer.MainLandType mapType) {
        return new MainMapInfo(mapType);
    }
    private MainMapInfo(MainServer.MainLandType mapType) {
        this.mapType = mapType;
    }
    // private List<string> GetConfigFilesByMapType(MapType mapType)
    // {
    //     var configFileNames = new List<string>();
    //     var mapName = GetMapName(mapType);
    //     configFileNames.Add($"Map_{mapName}");
    //     configFileNames.Add($"Map_{mapName}Building");
    //     configFileNames.Add($"Map_{mapName}Transfer");
    //     configFileNames.Add($"Map_{mapName}Env");
    //     configFileNames.Add($"Map_{mapName}EnvOutBuilding");
    //     configFileNames.Add($"Map_{mapName}MultipleOut");
    //     configFileNames.Add($"Map_{mapName}Interior");
    //     // switch (mapType)
    //     // {
    //     //     case MapType.HeartGold:
    //     //         configFileNames.Add($"{mapName}.json");
    //     //         break;
    //     //     case MapType.Platinum:
    //     //         configFileNames.Add($"{mapName}.json");
    //     //         break;
    //     //     case MapType.White:
    //     //         configFileNames.Add($"{mapName}.json");
    //     //         break;
    //     //     // default:
    //     //     //     configFileNames.Add("HeartGold.json");
    //     // }
    //     return configFileNames;
    // }
    // private List<string> GetConfigWaterFilesByMapType(MapType mapType)
    // {
    //     var configFileNames = new List<string>();
    //     var mapName = GetMapName(mapType);
    //     // configFileNames.Add($"Map_{mapName}");
    //     // configFileNames.Add($"Map_{mapName}Building");
    //     // configFileNames.Add($"Map_{mapName}Transfer");
    //     // configFileNames.Add($"Map_{mapName}Env");
    //     // // configFileNames.Add($"Map_{mapName}EnvOutBuilding");
    //     // configFileNames.Add($"Map_{mapName}MultipleOut");
    //     configFileNames.Add($"Map_{mapName}_WaterMap");
    //     // switch (mapType)
    //     // {
    //     //     case MapType.HeartGold:
    //     //         configFileNames.Add($"{mapName}.json");
    //     //         break;
    //     //     case MapType.Platinum:
    //     //         configFileNames.Add($"{mapName}.json");
    //     //         break;
    //     //     case MapType.White:
    //     //         configFileNames.Add($"{mapName}.json");
    //     //         break;
    //     //     // default:
    //     //     //     configFileNames.Add("HeartGold.json");
    //     // }
    //     return configFileNames;
    // }
    // private string GetMapNpcConfigFileName(MapType mapType)
    // {
    //     return "Map_NpcExport";
    //     switch (mapType)
    //     {
    //         case MapType.HeartGold:
    //             return "HeartGoldNPC.json";
    //         case MapType.Platinum:
    //             return "PlatinumNPC.json";
    //         case MapType.White:
    //             return "WhiteNPC.json";
    //         // default:
    //         //     return "HeartGoldNPC.json";
    //     }
    //     return null;
    // }
    private string? GetMapName()
    {
        // return "Platinum";
        // return "PrefabMapData";
        switch (mapType)
        {
            case MainServer.MainLandType.MainLandHeartGold:
                return "HeartGold";
            case MainServer.MainLandType.MainLandPlatinum:
                return "Platinum";
            case MainServer.MainLandType.MainLandWhite:
                return "White";
            // default:
            //     configFileNames.Add("HeartGold.json");
        }
        return null;
    }

    // public List<string> GetConfigFiles()
    // {
    //     var configFileNames = new List<string>();
    //     var mapName = GetMapName(mapType);
    //     configFileNames.Add($"Map_{mapName}");
    //     configFileNames.Add($"Map_{mapName}Building");
    //     configFileNames.Add($"Map_{mapName}Transfer");
    //     configFileNames.Add($"Map_{mapName}Env");
    //     configFileNames.Add($"Map_{mapName}EnvOutBuilding");
    //     configFileNames.Add($"Map_{mapName}MultipleOut");
    //     configFileNames.Add($"Map_{mapName}Interior");
    //     // switch (mapType)
    //     // {
    //     //     case MapType.HeartGold:
    //     //         configFileNames.Add($"{mapName}.json");
    //     //         break;
    //     //     case MapType.Platinum:
    //     //         configFileNames.Add($"{mapName}.json");
    //     //         break;
    //     //     case MapType.White:
    //     //         configFileNames.Add($"{mapName}.json");
    //     //         break;
    //     //     // default:
    //     //     //     configFileNames.Add("HeartGold.json");
    //     // }
    //     return configFileNames;
    // }

    // public List<string> GetConfigWaterFiles()
    // {
    //     var configFileNames = new List<string>();
    //     var mapName = GetMapName(mapType);
    //     // configFileNames.Add($"Map_{mapName}");
    //     // configFileNames.Add($"Map_{mapName}Building");
    //     // configFileNames.Add($"Map_{mapName}Transfer");
    //     // configFileNames.Add($"Map_{mapName}Env");
    //     // // configFileNames.Add($"Map_{mapName}EnvOutBuilding");
    //     // configFileNames.Add($"Map_{mapName}MultipleOut");
    //     configFileNames.Add($"Map_{mapName}_WaterMap");
    //     // switch (mapType)
    //     // {
    //     //     case MapType.HeartGold:
    //     //         configFileNames.Add($"{mapName}.json");
    //     //         break;
    //     //     case MapType.Platinum:
    //     //         configFileNames.Add($"{mapName}.json");
    //     //         break;
    //     //     case MapType.White:
    //     //         configFileNames.Add($"{mapName}.json");
    //     //         break;
    //     //     // default:
    //     //     //     configFileNames.Add("HeartGold.json");
    //     // }
    //     return configFileNames;
    // }

    // public string GetMapNpcConfigFileName()
    // {
    //     return "Map_NpcExport";
    //     switch (mapType)
    //     {
    //         case MapType.HeartGold:
    //             return "HeartGoldNPC.json";
    //         case MapType.Platinum:
    //             return "PlatinumNPC.json";
    //         case MapType.White:
    //             return "WhiteNPC.json";
    //         // default:
    //         //     return "HeartGoldNPC.json";
    //     }
    //     return null;
    // }
    public string GetMapNameId()
    {
        return GetMapName();
    }

    public MapDataPack GetMapData()
    {
        if(_mapData != null) {
            return _mapData;
        }
        _mapData = MapDataPack.LoadMapData("Map_"+GetMapName());
        return _mapData;
//         var json = AssertResourceLoader.Share.LoadAsset<TextAsset>("Map_"+GetMapName()).text;
//         // string json = JsonConvert.SerializeObject(myData, Formatting.Indented);
//         // _mapData = JsonConvert.DeserializeObject<MapData>(json);
// // 反序列化
// // var obj = JsonConvert.DeserializeObject<MapData>(json);
//         // var json = AssertResourceLoader.Share.LoadAsset<TextAsset>("Map_"+GetMapName()).text;
//         // // var list = JsonUtility.FromJson<PrefabDataList>(json);
//         // _mapData = JsonUtility.FromJson<MapData>(json);
//         var settings = new JsonSerializerSettings
//         {
//             Converters = new[] {
//                 new Vec3Conv(),
//                 //new StringEnumConverter(),
//             },
//         };
//         _mapData = JsonConvert.DeserializeObject<MapData>(json, settings);
//         return _mapData;
        // return JsonConvert.DeserializeObject<MapData>(json, settings);
        // var myObjectINeedToSerialize = new Vector3(1, 2, 3);

        // var json = JsonConvert.SerializeObject(myObjectINeedToSerialize, settings);
        // ⭐ 用 Json.NET 来序列化 Dictionary
        // string json = JsonConvert.SerializeObject(combinedData, settings);
        // _mapData
    }

    public MapEnvInfo? GetEnvInfo()
    {
        return null;
    }

    public MainLandType GetMainLandType()
    {
        return mapType;
    }
}