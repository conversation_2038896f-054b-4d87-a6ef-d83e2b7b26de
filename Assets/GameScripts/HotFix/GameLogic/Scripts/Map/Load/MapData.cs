// ====== 数据结构 ======
using System.Collections.Generic;
using Newtonsoft.Json;
using UnityEngine;
using MessagePack;
[MessagePackObject]
public class MapDataPack
{
    [Key(0)]
    public int gridSize;
    [Key(1)]
    public float cellSize;
    [Key(2)]
    public Dictionary<Vector2Int, MapBlock> mainBlocks;
    // public MapCellData mapCellDatas;
    [Key(3)]
    public Dictionary<Vector2Int, MapInitPostionPack> initPostions;
    [Key(4)]
    public Dictionary<Vector2Int, List<MapNPCInfoPack>> npcs;
    [Key(5)]
    public Dictionary<Vector2Int, List<MapOtherUnitInfoPack>> otherUnits;
    [Key(6)]
    public Dictionary<Vector2Int, List<MapRuntimeObstacleInfoPack>> runtimeObstacles;
    [Key(7)]
    public Dictionary<Vector2Int, List<MapTriggerEventComponentInfoPack>> triggerEventComponents;

    public static MapDataPack LoadMapData(string mapJsonName)
    {
        var bytes = AssertResourceLoader.Share.LoadAsset<TextAsset>(mapJsonName).bytes;
        MapDataPack mapData = MessagePackSerializer.Deserialize<MapDataPack>(bytes);
        return mapData;
    }
    // public static MapData LoadMapData(string mapJsonName)
    // {
    //     var json = AssertResourceLoader.Share.LoadAsset<TextAsset>(mapJsonName).text;
    //     var settings = new JsonSerializerSettings
    //     {
    //         Converters = new[] {
    //             new Vec3Conv(),
    //             //new StringEnumConverter(),
    //         },
    //     };
    //     var mapData = JsonConvert.DeserializeObject<MapData>(json, settings);
    //     return mapData;
    // }
}
[MessagePackObject]
public class MapOtherUnitInfoPack
{
    [Key(0)]
    public string name;
    [Key(1)]
    public Vector3 position;
    [Key(2)]
    public Vector3 rotation;
    [Key(3)]
    public Vector3 scale;
    [Key(4)]
    public string prefabAddress;
    [Key(5)]
    public MapTransmitPointInfoPack transmitMapInfo;
}
[MessagePackObject]
public class MapNPCInfoPack
{
    [Key(0)]
    public string name;
    [Key(1)]
    public string imageName;
    [Key(2)]
    public Vector3 position;
    [Key(3)]
    public bool defaultIsHiden;
    [Key(4)]
    public List<MapYarnOprationInfoPack> oprationDatas;
}
[MessagePackObject]
public class MapYarnOprationInfoPack
{
    // public List<string> yarnTitleList; //如果需要多个就用这个设置
    // public List<MapYarnOprationType> yarnOprationTypeList;//如果需要多个就用这个设置
    // public List<string> yarnOprationValues;//对应操作的值，比如要转成什么状态
    // public List<string> yarnTitleList; //如果需要多个就用这个设置 每一个title都有对应开始和结束
    // public List<MapYarnOprationType> yarnStartOprationTypeList;//如果需要多个就用这个设置
    // public List<MapYarnOprationType> yarnEndOprationTypeList;
    // public List<string> yarnStartOprationValues;//对应操作的值，比如要转成什么状态
    // public List<string> yarnEndOprationValues;
    [Key(0)]
    public string yarnTitle;
    [Key(1)]
    public MapYarnOprationType yarnStartOprationType;
    [Key(2)]
    public MapYarnOprationType yarnEndOprationType;
    [Key(3)]
    public string yarnStartOprationValue;
    [Key(4)]
    public string yarnEndOprationValue;
}
[MessagePackObject]
public class MapInitPostionPack
{
    // public string gen;
    [Key(0)]
    public string tagName;
    [Key(1)]
    public Vector3 position;
    [Key(2)]
    public MapTransmitPointInfoPack transmitMapInfo;
}
[MessagePackObject]
public class MapTransmitPointInfoPack
{
    [Key(0)]
    public string name;
    [Key(1)]
    public string toMapName;
    [Key(2)]
    public Vector3 position;
    [Key(3)]
    public Vector3 localScale;
    [Key(4)]
    public Vector3 boxColliderSize;
    [Key(5)]
    public bool isHidenEffect;
    [Key(6)]
    public bool isPokeCenter;
}
// [MessagePackObject]
// public class MapCellData {
//     [Key(0)]
//     public int gridSize;
//     [Key(1)]
//     public float cellSize;
//     [Key(2)]
//     public Dictionary<Vector2Int, MapBlock> blocks;
// }

[MessagePackObject]
public class MapBlock {
    [Key(0)]
    public string blockName;
    [Key(1)]
    public Vector3 localPosition;
    [Key(2)]
    public string prefabAddress; //需要加载的地图块路径
    [Key(3)]
    public MapCellInfo mapCellInfo;
    [Key(4)]
    public Dictionary<Vector2Int, MapCellTileData> tiles;
}

[MessagePackObject]
public class MapCellTileData {
    [Key(0)]
    public Vector2Int coord;
    [Key(1)]
    public float surfaceY;
    [Key(2)]
    public MapCellInfo info; // 表示有特别备注的信息,如果为null就用父类的
    [Key(3)]
    public TileType tileType;
    [Key(4)]
    public bool IsBattle;
}

[MessagePackObject]
public class MapCellInfo {
    [Key(0)]
    public string Bgm;
    [Key(1)]
    public string RegionId;
    [Key(2)]
    public string AreaId;
    [Key(3)]
    public string BattleBgm;
    [Key(4)]
    public MapTileType TileType = MapTileType.Grass;
}
public enum TileType {
    NotWalkable,
    Grass,
    Water,
    Walkable
}
[MessagePackObject]
public class MapTriggerEventComponentInfoPack
{
    [Key(0)]
    public string eventName;
    [Key(1)]
    public string eventValue;
    [Key(2)]
    public string prefab;
    [Key(3)]
    public MapTriggerEventType triggerEventType;
    [Key(4)]
    public Vector3 position;
    [Key(5)]
    public Vector3 localScale;
    [Key(6)]
    public Vector3 boxColliderSize;
    [Key(7)]
    public List<MapYarnOprationInfoPack> oprationDatas;
}
[MessagePackObject]
public class MapRuntimeObstacleInfoPack
{
    [Key(0)]
    public string name;
    [Key(1)]
    public Vector3 position;
    [Key(2)]
    public Vector3 scale;
    [Key(3)]
    public string prefab;
    [Key(4)]
    public bool defaultIsHiden;
    [Key(5)]
    public List<MapYarnOprationInfoPack> oprationDatas;
}
// [System.Serializable]
// public class MapCellData {
//     public int gridSize;
//     public float cellSize;
//     public Dictionary<Vector2Int, MapBlock> blocks;
//     // public List<MapBlock> blocks;
// }
// [System.Serializable]
// public class MapBlock
// {
//     public string blockName;
//     public Vector3 localPosition;
//     public MapCellInfo mapCellInfo;
//     public Dictionary<Vector2Int, MapCellTileData> tiles;
// }

// [System.Serializable]
// public class MapCellTileData
// {
//     public Vector2Int coord;
//     public float surfaceY;
//     public MapCellInfo info; //表示有特别备注的信息,如果为null就用父类的
//     public TileType tileType;
//     public bool IsBattle = false;
// }
// public enum TileType {
//     NotWalkable,
//     Grass,
//     Water,
//     Walkable
// }
// [System.Serializable]
// public class MapCellInfo {
//     public string Bgm;
//     public string RegionId;
//     public string AreaId;
//     public string BattleBgm;
//     public MapTileType TileType = MapTileType.Grass;
//     // public bool Walkable = false;
// }

// [System.Serializable]
// public class PrefabData
// {
//     public string name;
//     public Vector3 position;
//     public Vector3 rotation;
//     public Vector3 scale;
//     public string prefab;
//     public PrefabTransmitMapInfo transmitMapInfo;
//     public Dictionary<string, PrefabMapTileInfo> mapTileInfos;
// }

// [System.Serializable]
// public class PrefabTransmitMapInfo
// {
//     public string name;
//     public string toMapName;
//     public Vector3 position;
//     public Vector3 localScale;
//     public Vector3 boxColliderSize;
//     public bool isHidenEffect;
//     public bool isPokeCenter;
// }

// [System.Serializable]
// public class MapTriggerEventComponentInfo
// {
//     public string eventName;
//     public string eventValue;
//     public string prefab;
//     public MapTriggerEventType triggerEventType;
//     public Vector3 position;
//     public Vector3 localScale;
//     public Vector3 boxColliderSize;
//     public List<MapYarnOprationData> oprationDatas;
// }

// [System.Serializable]
// public class PrefabMapTileInfo {
//     public string Bgm;
//     public string RegionId;
//     public string AreaId;
//     public string BattleBgm;
//     public bool IsBattle = false;
//     public MapTileType TileType = MapTileType.Grass;
//     public bool Walkable = false;
// }

// [System.Serializable]
// public class WaterMapData
// {
//     public int gridSize;
//     public float cellSize;
//     public List<WaterMapBlock> blocks;
// }

// [System.Serializable]
// public class WaterMapBlock
// {
//     public string blockName;
//     public Vector3 localPosition;
//     public WaterCell[] waterCells;
// }

// [System.Serializable]
// public class WaterCell
// {
//     public Vector2Int coord;
//     public float surfaceY;
//     public bool isWaterEdge;
// }
// // [System.Serializable]
// // public class NPCExportData
// // {
// //     public string name;
// //     public Vector3 position;
// // }
// // [System.Serializable]
// // private class NPCDataList
// // {
// //     public List<NPCData> data;
// // }

// [System.Serializable]
// public class MapNPCData
// {
//     public string name;
//     public string imageName;
//     public Vector3 position;
//     public bool defaultIsHiden;
//     public List<MapYarnOprationData> oprationDatas;
// }
// [System.Serializable]
// public class MapRuntimeObstacleData
// {
//     public string name;
//     public Vector3 position;
//     public Vector3 scale;
//     public string prefab;
//     public bool defaultIsHiden;
//     public List<MapYarnOprationData> oprationDatas;
// }

// [System.Serializable]
// public class MapYarnOprationData
// {
//     // public List<string> yarnTitleList; //如果需要多个就用这个设置
//     // public List<MapYarnOprationType> yarnOprationTypeList;//如果需要多个就用这个设置
//     // public List<string> yarnOprationValues;//对应操作的值，比如要转成什么状态
//     // public List<string> yarnTitleList; //如果需要多个就用这个设置 每一个title都有对应开始和结束
//     // public List<MapYarnOprationType> yarnStartOprationTypeList;//如果需要多个就用这个设置
//     // public List<MapYarnOprationType> yarnEndOprationTypeList;
//     // public List<string> yarnStartOprationValues;//对应操作的值，比如要转成什么状态
//     // public List<string> yarnEndOprationValues;
//     public string yarnTitle;
//     public MapYarnOprationType yarnStartOprationType;
//     public MapYarnOprationType yarnEndOprationType;
//     public string yarnStartOprationValue;
//     public string yarnEndOprationValue;
// }

// [System.Serializable]
// public class MapInitPostion
// {
//     // public string gen;
//     public string tagName;
//     public Vector3 position;
//     public PrefabTransmitMapInfo transmitMapInfo;
// }

// [System.Serializable]
// public class MapData
// {
//     public List<PrefabData> mainMap;
//     public List<PrefabData> buildingMap;
//     public List<PrefabData> transferMap;
//     public List<PrefabData> envMap;
//     public List<PrefabData> envOutBuildingMap;
//     public List<PrefabData> multipleOutMap;
//     public List<PrefabData> interiorMap;
//     public List<PrefabData> otherMap;
//     // public Dictionary<string, List<PrefabData>> prefabData;
//     public WaterMapData waterMap;
//     public List<MapNPCData> npcs;
//     public List<MapInitPostion> initPostions;
//     public List<MapRuntimeObstacleData> runtimeObstacles;
//     public List<MapTriggerEventComponentInfo> triggerEventComponents;
//     public static MapData LoadMapData(string mapJsonName)
//     {
//         var json = AssertResourceLoader.Share.LoadAsset<TextAsset>(mapJsonName).text;
//         var settings = new JsonSerializerSettings
//         {
//             Converters = new[] {
//                 new Vec3Conv(),
//                 //new StringEnumConverter(),
//             },
//         };
//         var mapData = JsonConvert.DeserializeObject<MapData>(json, settings);
//         return mapData;
//     }
// }