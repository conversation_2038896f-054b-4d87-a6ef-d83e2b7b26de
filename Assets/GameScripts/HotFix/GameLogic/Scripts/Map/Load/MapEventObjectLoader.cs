using UnityEngine;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using System.Linq;
using UnityEngine.AI;

/// <summary>
/// 地图事件对象加载器，负责动态加载运行时障碍物和触发事件组件
/// 参考NPCLoader和PrefabRegionLoader的模式实现
/// </summary>
public class MapEventObjectLoader
{
    private Transform mapContainer;
    private float loadRadius = 50f;
    private float unloadRadius => loadRadius * 1.5f;

    // 运行时障碍物相关
    private List<MapRuntimeObstacleInfoPack> _allRuntimeObstacles = new();
    private Dictionary<string, LoadedRuntimeObstacle> _loadedObstacles = new();
    private HashSet<Vector3> _loadedObstaclePositions = new();

    // 触发事件组件相关
    private List<MapTriggerEventComponentInfoPack> _allTriggerEvents = new();
    private Dictionary<string, LoadedTriggerEvent> _loadedTriggerEvents = new();
    private HashSet<Vector3> _loadedTriggerPositions = new();

    private Vector3? _lastCenter = null;
    private bool _isLoading = false;

    // Yarn节点状态管理
    private List<MainServer.ThroughPointInfo> _throughPoints = new();
    private Vector3? _lastLoadWorldPosition = null;

    /// <summary>
    /// 已加载的运行时障碍物信息
    /// </summary>
    private class LoadedRuntimeObstacle
    {
        public GameObject Instance;
        public Vector3 Position;
        public MapRuntimeObstacleInfoPack Data;
    }

    /// <summary>
    /// 已加载的触发事件信息
    /// </summary>
    private class LoadedTriggerEvent
    {
        public GameObject Instance;
        public Vector3 Position;
        public MapTriggerEventComponentInfoPack Data;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="mapContainer">地图容器Transform</param>
    /// <param name="runtimeObstacles">运行时障碍物数据列表</param>
    /// <param name="triggerEvents">触发事件组件数据列表</param>
    public MapEventObjectLoader(Transform mapContainer,
        List<MapRuntimeObstacleInfoPack> runtimeObstacles,
        List<MapTriggerEventComponentInfoPack> triggerEvents)
    {
        this.mapContainer = mapContainer;
        _allRuntimeObstacles = runtimeObstacles ?? new List<MapRuntimeObstacleInfoPack>();
        _allTriggerEvents = triggerEvents ?? new List<MapTriggerEventComponentInfoPack>();

        Debug.Log($"MapEventObjectLoader初始化: {_allRuntimeObstacles.Count}个运行时障碍物, {_allTriggerEvents.Count}个触发事件");
    }

    /// <summary>
    /// 设置加载半径
    /// </summary>
    public void SetLoadRadius(float radius)
    {
        loadRadius = radius;
    }

    /// <summary>
    /// 根据位置动态加载附近的事件对象
    /// </summary>
    public void LoadNearbyEventObjects(Vector3 worldPosition)
    {
        LoadNearbyEventObjectsAsync(worldPosition).Forget();
    }

    /// <summary>
    /// 异步加载附近事件对象的主逻辑
    /// </summary>
    public async UniTask LoadNearbyEventObjectsAsync(Vector3 worldPosition)
    {
        if (_isLoading) return;
        if (mapContainer == null)
        {
            Debug.LogError("MapEventObjectLoader: Map container is not set.");
            return;
        }

        _isLoading = true;

        // 记录最后加载的世界位置，用于UpdateThroughPoints时重新加载
        _lastLoadWorldPosition = worldPosition;

        // 转换为本地坐标
        Vector3 localPosition = mapContainer.InverseTransformPoint(worldPosition);

        // 检查是否需要更新（避免频繁加载）
        if (_lastCenter.HasValue && Vector3.Distance(_lastCenter.Value, localPosition) < loadRadius * 0.3f)
        {
            _isLoading = false;
            return;
        }

        _lastCenter = localPosition;

        // 卸载远距离的对象
        await UnloadDistantObjects(localPosition);

        // 加载运行时障碍物
        await LoadNearbyRuntimeObstacles(localPosition);

        // 加载触发事件组件
        await LoadNearbyTriggerEvents(localPosition);

        _isLoading = false;
    }

    /// <summary>
    /// 更新Yarn节点状态，触发事件对象的显示/隐藏逻辑
    /// 参考NPCLoader的UpdateThroughPoints实现
    /// </summary>
    /// <param name="throughPoints">当前到达的Yarn节点列表</param>
    public async UniTask UpdateThroughPoints(List<MainServer.ThroughPointInfo> throughPoints)
    {
        _throughPoints = throughPoints;
        _lastCenter = null; // 重置缓存，强制重新评估所有对象

        // 清空位置缓存，强制重新加载和评估
        _loadedObstaclePositions.Clear();
        _loadedTriggerPositions.Clear();

        // 如果有上次的加载位置，重新加载该位置的对象
        if (_lastLoadWorldPosition.HasValue)
        {
            await LoadNearbyEventObjectsAsync(_lastLoadWorldPosition.Value);
        }
    }

    /// <summary>
    /// 卸载远距离的对象
    /// </summary>
    private async UniTask UnloadDistantObjects(Vector3 centerPosition)
    {
        // 卸载远距离的运行时障碍物
        var obstaclesToUnload = _loadedObstacles.Where(kvp =>
            Vector3.Distance(kvp.Value.Position, centerPosition) > unloadRadius).ToList();

        foreach (var obstacle in obstaclesToUnload)
        {
            if (obstacle.Value.Instance != null)
            {
                GameObject.Destroy(obstacle.Value.Instance);
            }
            _loadedObstacles.Remove(obstacle.Key);
            _loadedObstaclePositions.Remove(obstacle.Value.Position);
        }

        // 卸载远距离的触发事件
        var eventsToUnload = _loadedTriggerEvents.Where(kvp =>
            Vector3.Distance(kvp.Value.Position, centerPosition) > unloadRadius).ToList();

        foreach (var triggerEvent in eventsToUnload)
        {
            if (triggerEvent.Value.Instance != null)
            {
                GameObject.Destroy(triggerEvent.Value.Instance);
            }
            _loadedTriggerEvents.Remove(triggerEvent.Key);
            _loadedTriggerPositions.Remove(triggerEvent.Value.Position);
        }

        if (obstaclesToUnload.Count > 0 || eventsToUnload.Count > 0)
        {
            Debug.Log($"卸载了 {obstaclesToUnload.Count} 个障碍物和 {eventsToUnload.Count} 个触发事件");
        }

        await UniTask.Yield();
    }

    /// <summary>
    /// 加载附近的运行时障碍物，支持局部刷新和Yarn状态处理
    /// 参考NPCLoader的SpawnNPC逻辑
    /// </summary>
    private async UniTask LoadNearbyRuntimeObstacles(Vector3 centerPosition)
    {
        foreach (var obstacleData in _allRuntimeObstacles)
        {
            float distance = Vector3.Distance(obstacleData.position, centerPosition);

            // 检查是否在加载范围内
            if (distance <= loadRadius)
            {
                await SpawnRuntimeObstacle(obstacleData);
            }
        }
    }

    /// <summary>
    /// 生成运行时障碍物，支持局部刷新和Yarn状态处理
    /// 参考NPCLoader的SpawnNPC实现
    /// </summary>
    private async UniTask SpawnRuntimeObstacle(MapRuntimeObstacleInfoPack obstacleData)
    {
        string key = $"{obstacleData.name}_{obstacleData.position}";
        LoadedRuntimeObstacle loadedObstacle = null;
        bool shouldBeHidden = obstacleData.defaultIsHiden;

        // 检查是否已经加载过这个障碍物
        _loadedObstacles.TryGetValue(key, out loadedObstacle);

        // 处理Yarn操作数据，确定当前状态
        if (obstacleData.oprationDatas != null && obstacleData.oprationDatas.Count > 0)
        {
            foreach (var throughPoint in _throughPoints)
            {
                var oprationData = obstacleData.oprationDatas.Find(item => item.yarnTitle == throughPoint.PointTitle);
                if (oprationData != null)
                {
                    switch (throughPoint.InfoType)
                    {
                        case MainServer.ThroughPointInfoType.Start:
                            switch (oprationData.yarnStartOprationType)
                            {
                                case MapYarnOprationType.Hide:
                                    shouldBeHidden = true;
                                    if (loadedObstacle != null) // 已经加载过这个障碍物，需要隐藏
                                    {
                                        RemoveRuntimeObstacle(loadedObstacle);
                                        return; // 隐藏后直接返回，不需要继续处理
                                    }
                                    break;
                                case MapYarnOprationType.Show:
                                    shouldBeHidden = false;
                                    break;
                                case MapYarnOprationType.ChangeStatus:
                                    // 可以根据yarnStartOprationValue来处理具体的状态变化
                                    break;
                            }
                            break;
                        case MainServer.ThroughPointInfoType.End:
                            switch (oprationData.yarnEndOprationType)
                            {
                                case MapYarnOprationType.Hide:
                                    shouldBeHidden = true;
                                    if (loadedObstacle != null) // 已经加载过这个障碍物，需要隐藏
                                    {
                                        RemoveRuntimeObstacle(loadedObstacle);
                                        return; // 隐藏后直接返回，不需要继续处理
                                    }
                                    break;
                                case MapYarnOprationType.Show:
                                    shouldBeHidden = false;
                                    break;
                                case MapYarnOprationType.ChangeStatus:
                                    // 可以根据yarnEndOprationValue来处理具体的状态变化
                                    break;
                            }
                            break;
                    }
                }
            }
        }

        // 如果应该隐藏且没有加载过，直接返回不创建
        if (shouldBeHidden && loadedObstacle == null)
        {
            return;
        }

        // 如果应该显示但还没有加载，或者需要重新创建
        if (!shouldBeHidden && loadedObstacle == null && !_loadedObstaclePositions.Contains(obstacleData.position))
        {
            await CreateRuntimeObstacle(obstacleData);
        }
        // 如果已经加载且应该显示，确保对象是激活的
        else if (!shouldBeHidden && loadedObstacle != null)
        {
            if (loadedObstacle.Instance != null)
            {
                loadedObstacle.Instance.SetActive(true);
            }
        }
    }

    /// <summary>
    /// 创建运行时障碍物
    /// </summary>
    private async UniTask CreateRuntimeObstacle(MapRuntimeObstacleInfoPack obstacleData)
    {
        GameObject obstacleObj = null;

        // 如果有预制体，则加载预制体；否则创建空GameObject
        if (!string.IsNullOrEmpty(obstacleData.prefab))
        {
            try
            {
                GameObject prefab = await AssertResourceLoader.Share.LoadAssetAsync<GameObject>(obstacleData.prefab);
                if (prefab != null)
                {
                    var objs = await GameObject.InstantiateAsync(prefab, mapContainer).ToUniTask();
                    obstacleObj = objs[0];
                    obstacleObj.name = obstacleData.name; // 设置为数据中的名称
                    var navMeshObstacle = obstacleObj.AddComponent<NavMeshObstacle>();
                    navMeshObstacle.carving = true;
                }
                else
                {
                    Debug.LogWarning($"无法加载运行时障碍物预制体: {obstacleData.prefab}，将创建空GameObject");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"加载运行时障碍物预制体失败: {obstacleData.prefab}, 错误: {e.Message}");
            }
        }

        // 如果预制体加载失败或没有预制体，创建空GameObject
        if (obstacleObj == null)
        {
            obstacleObj = new GameObject(obstacleData.name);
            obstacleObj.transform.SetParent(mapContainer, false);
        }

        // 设置位置和缩放
        obstacleObj.transform.localPosition = obstacleData.position;
        obstacleObj.transform.localScale = obstacleData.scale;

        // 获取或添加MapRuntimeObstacle组件
        if (!obstacleObj.TryGetComponent<MapRuntimeObstacle>(out var obstacleComponent))
        {
            obstacleComponent = obstacleObj.AddComponent<MapRuntimeObstacle>();
        }
        obstacleComponent.defaultIsHiden = obstacleData.defaultIsHiden;

        // 设置Yarn操作数据
        if (obstacleData.oprationDatas != null && obstacleData.oprationDatas.Count > 0)
        {
            var firstOpData = obstacleData.oprationDatas[0];
            obstacleComponent.yarnTitle = firstOpData.yarnTitle;
            obstacleComponent.yarnStartOprationType = firstOpData.yarnStartOprationType;
            obstacleComponent.yarnEndOprationType = firstOpData.yarnEndOprationType;

            // 如果有多个操作数据，设置到列表中
            if (obstacleData.oprationDatas.Count > 1)
            {
                obstacleComponent.yarnTitleList = new List<string>();
                obstacleComponent.yarnStartOprationTypeList = new List<MapYarnOprationType>();
                obstacleComponent.yarnEndOprationTypeList = new List<MapYarnOprationType>();
                obstacleComponent.yarnStartOprationValues = new List<string>();
                obstacleComponent.yarnEndOprationValues = new List<string>();

                foreach (var opData in obstacleData.oprationDatas)
                {
                    obstacleComponent.yarnTitleList.Add(opData.yarnTitle);
                    obstacleComponent.yarnStartOprationTypeList.Add(opData.yarnStartOprationType);
                    obstacleComponent.yarnEndOprationTypeList.Add(opData.yarnEndOprationType);
                    obstacleComponent.yarnStartOprationValues.Add(opData.yarnStartOprationValue);
                    obstacleComponent.yarnEndOprationValues.Add(opData.yarnEndOprationValue);
                }
            }
        }

        // 根据defaultIsHiden设置初始状态
        obstacleObj.SetActive(!obstacleData.defaultIsHiden);

        // 记录已加载的障碍物
        string key = $"{obstacleData.name}_{obstacleData.position}";
        _loadedObstacles[key] = new LoadedRuntimeObstacle
        {
            Instance = obstacleObj,
            Position = obstacleData.position,
            Data = obstacleData
        };
        _loadedObstaclePositions.Add(obstacleData.position);

        await UniTask.Yield();
    }

    /// <summary>
    /// 移除运行时障碍物
    /// 参考NPCLoader的RemoveNpc实现
    /// </summary>
    private void RemoveRuntimeObstacle(LoadedRuntimeObstacle obstacle)
    {
        if (obstacle.Instance != null)
        {
            GameObject.Destroy(obstacle.Instance);
        }
        _loadedObstaclePositions.Remove(obstacle.Position);
        string key = $"{obstacle.Data.name}_{obstacle.Data.position}";
        _loadedObstacles.Remove(key);
    }

    /// <summary>
    /// 加载附近的触发事件组件，支持局部刷新和Yarn状态处理
    /// 参考NPCLoader的SpawnNPC逻辑
    /// </summary>
    private async UniTask LoadNearbyTriggerEvents(Vector3 centerPosition)
    {
        foreach (var eventData in _allTriggerEvents)
        {
            float distance = Vector3.Distance(eventData.position, centerPosition);

            // 检查是否在加载范围内
            if (distance <= loadRadius)
            {
                await SpawnTriggerEvent(eventData);
            }
        }
    }

    /// <summary>
    /// 生成触发事件组件，支持局部刷新和Yarn状态处理
    /// 参考NPCLoader的SpawnNPC实现
    /// </summary>
    private async UniTask SpawnTriggerEvent(MapTriggerEventComponentInfoPack eventData)
    {
        string key = $"{eventData.eventName}_{eventData.position}";
        LoadedTriggerEvent loadedTriggerEvent = null;
        bool shouldBeHidden = false; // 触发事件默认不隐藏，除非有特殊配置

        // 检查是否已经加载过这个触发事件
        _loadedTriggerEvents.TryGetValue(key, out loadedTriggerEvent);

        // 注意：触发事件组件的oprationDatas在MapTriggerEventComponentInfo中没有定义
        // 如果需要支持Yarn状态控制，需要在数据结构中添加oprationDatas字段
        // 这里暂时保留框架，以备将来扩展

        // 如果应该隐藏且没有加载过，直接返回不创建
        if (shouldBeHidden && loadedTriggerEvent == null)
        {
            return;
        }

        // 如果应该显示但还没有加载，或者需要重新创建
        if (!shouldBeHidden && loadedTriggerEvent == null && !_loadedTriggerPositions.Contains(eventData.position))
        {
            await CreateTriggerEvent(eventData);
        }
        // 如果已经加载且应该显示，确保对象是激活的
        else if (!shouldBeHidden && loadedTriggerEvent != null)
        {
            if (loadedTriggerEvent.Instance != null)
            {
                loadedTriggerEvent.Instance.SetActive(true);
            }
        }
    }

    /// <summary>
    /// 创建触发事件组件
    /// </summary>
    private async UniTask CreateTriggerEvent(MapTriggerEventComponentInfoPack eventData)
    {
        GameObject eventObj = null;

        // 如果有预制体，则加载预制体；否则创建空GameObject
        if (!string.IsNullOrEmpty(eventData.prefab))
        {
            try
            {
                GameObject prefab = await AssertResourceLoader.Share.LoadAssetAsync<GameObject>(eventData.prefab);
                if (prefab != null)
                {
                    var objs = await GameObject.InstantiateAsync(prefab, mapContainer).ToUniTask();
                    eventObj = objs[0];
                    eventObj.name = $"TriggerEvent_{eventData.eventName}"; // 设置为事件名称
                }
                else
                {
                    Debug.LogWarning($"无法加载触发事件预制体: {eventData.prefab}，将创建空GameObject");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"加载触发事件预制体失败: {eventData.prefab}, 错误: {e.Message}");
            }
        }

        // 如果预制体加载失败或没有预制体，创建空GameObject
        if (eventObj == null)
        {
            eventObj = new GameObject($"TriggerEvent_{eventData.eventName}");
            eventObj.transform.SetParent(mapContainer, false);
        }

        // 设置位置和缩放
        eventObj.transform.localPosition = eventData.position;
        eventObj.transform.localScale = eventData.localScale;

        // 获取或添加BoxCollider作为触发器
        if (!eventObj.TryGetComponent<BoxCollider>(out var boxCollider))
        {
            boxCollider = eventObj.AddComponent<BoxCollider>();
        }
        boxCollider.isTrigger = true;
        boxCollider.size = eventData.boxColliderSize;

        // 获取或添加MapTriggerEventComponent组件
        if (!eventObj.TryGetComponent<MapTriggerEventComponent>(out var triggerComponent))
        {
            triggerComponent = eventObj.AddComponent<MapTriggerEventComponent>();
        }
        triggerComponent.eventName = eventData.eventName;
        triggerComponent.eventValue = eventData.eventValue;
        triggerComponent.triggerEventType = eventData.triggerEventType;

        // 记录已加载的触发事件
        string key = $"{eventData.eventName}_{eventData.position}";
        _loadedTriggerEvents[key] = new LoadedTriggerEvent
        {
            Instance = eventObj,
            Position = eventData.position,
            Data = eventData
        };
        _loadedTriggerPositions.Add(eventData.position);

        await UniTask.Yield();
    }

    /// <summary>
    /// 移除触发事件组件
    /// 参考NPCLoader的RemoveNpc实现
    /// </summary>
    private void RemoveTriggerEvent(LoadedTriggerEvent triggerEvent)
    {
        if (triggerEvent.Instance != null)
        {
            GameObject.Destroy(triggerEvent.Instance);
        }
        _loadedTriggerPositions.Remove(triggerEvent.Position);
        string key = $"{triggerEvent.Data.eventName}_{triggerEvent.Data.position}";
        _loadedTriggerEvents.Remove(key);
    }

    /// <summary>
    /// 清理所有已加载的对象
    /// </summary>
    public void ClearAllLoadedObjects()
    {
        // 清理运行时障碍物
        foreach (var obstacle in _loadedObstacles.Values)
        {
            if (obstacle.Instance != null)
            {
                GameObject.Destroy(obstacle.Instance);
            }
        }
        _loadedObstacles.Clear();
        _loadedObstaclePositions.Clear();

        // 清理触发事件
        foreach (var triggerEvent in _loadedTriggerEvents.Values)
        {
            if (triggerEvent.Instance != null)
            {
                GameObject.Destroy(triggerEvent.Instance);
            }
        }
        _loadedTriggerEvents.Clear();
        _loadedTriggerPositions.Clear();

        _lastCenter = null;
        Debug.Log("MapEventObjectLoader: 已清理所有加载的对象");
    }

    /// <summary>
    /// 获取已加载的运行时障碍物数量
    /// </summary>
    public int GetLoadedObstacleCount()
    {
        return _loadedObstacles.Count;
    }

    /// <summary>
    /// 获取已加载的触发事件数量
    /// </summary>
    public int GetLoadedTriggerEventCount()
    {
        return _loadedTriggerEvents.Count;
    }

    /// <summary>
    /// 根据名称获取已加载的运行时障碍物
    /// </summary>
    public GameObject GetLoadedObstacle(string obstacleName)
    {
        var obstacle = _loadedObstacles.Values.FirstOrDefault(o => o.Data.name == obstacleName);
        return obstacle?.Instance;
    }

    /// <summary>
    /// 根据名称获取已加载的触发事件
    /// </summary>
    public GameObject GetLoadedTriggerEvent(string eventName)
    {
        var triggerEvent = _loadedTriggerEvents.Values.FirstOrDefault(e => e.Data.eventName == eventName);
        return triggerEvent?.Instance;
    }

    /// <summary>
    /// 强制更新指定位置的对象（用于动态数据更新）
    /// </summary>
    public async UniTask ForceUpdateAtPosition(Vector3 worldPosition)
    {
        if (_isLoading) return;

        _lastCenter = null; // 重置缓存，强制更新
        await LoadNearbyEventObjectsAsync(worldPosition);
    }
}
