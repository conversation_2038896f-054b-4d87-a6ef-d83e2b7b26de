using System.Collections.Generic;
using UnityEngine;
using System.IO;
using Cysharp.Threading.Tasks;
using UnityEngine.AI;

public class MapLoader : MonoBehaviour
{
    // public NinCharacter characterPrefab;
    public Transform npcContainer;
    private PrefabRegionLoader _prefabRegionLoader;
    private NPCLoader _npcLoader;
    private MapEventObjectLoader _mapEventObjectLoader;
    private RuntimeNavMeshLoader _navMeshLoader = new();
    // private WaterMapChecker _waterMapChecker;
    private float loadRadius = 50f;
    private IMapInfo _mapInfo;
    
    public IMapInfo GetMapInfo() {
        return _mapInfo;
    }
    public void SetMapInfo(IMapInfo mapInfo) {
        _mapInfo = mapInfo;
        ClearMap();
        ConfigMap(mapInfo);
    }

    public MainServer.MainLandType GetMainLandType() {
        var mapInfo = _mapInfo as MainMapInfo;
        if(mapInfo != null) {
            return mapInfo.mapType;
        }
        return MainServer.MainLandType.MainLandNone;
    }
    public async UniTask UpdateThroughPoints(List<MainServer.ThroughPointInfo> throughPoints) {
        await _npcLoader.UpdateThroughPoints(throughPoints);
        // 同时更新事件对象的Yarn状态
        if (_mapEventObjectLoader != null)
        {
            await _mapEventObjectLoader.UpdateThroughPoints(throughPoints);
        }
    }
    public void UpdatePostion(Vector3 position)
    {
        // _prefabRegionLoader.Update();
        // _prefabRegionLoader.UpdateGPUInstancing(position);
    }
    // public void UpdateNpcInfos(List<MainServer.NpcRoleConfig> roleConfigs) {
    //     _npcLoader.UpdateNpcInfos(roleConfigs);
    // }
    // public void UpdateObstacleInfos() {

    // }
    // public Vector3 GetDefaultInitPosition() {
    //     return GetInitPosition(null, MapController.Current.mapCharacterMgr.characterContainer);
    // }
    // public Vector3 GetInitPosition(string tagName, IMapInfo mapInfo) {
    //     var pos = mapInfo.GetInitPosition(tagName);
    //     return MapController.Current.mapCharacterMgr.characterContainer.TransformPoint(this.transform.TransformPoint(pos));
    // }
    // public Vector3 GetInitPosition(string tagName, Transform targetTransform = null)
    // {
    //     if(targetTransform == null) {
    //         targetTransform = MapController.Current.mapCharacterMgr.characterContainer;
    //     }
    //     var pos = _mapInfo.GetInitPosition(tagName);
    //     return targetTransform.TransformPoint(this.transform.TransformPoint(pos));
    //     // var mapData = GetMapData();
    //     // if(mapData.initPostions == null || mapData.initPostions.Count == 0) {
    //     //     return Vector3.zero;
    //     // }
    //     // if(string.IsNullOrEmpty(gen)) {
    //     //     return mapData.initPostions[0].position;
    //     // }
    //     // foreach (var item in mapData.initPostions)
    //     // {
    //     //     if(item.gen == gen) {
    //     //         return item.position;
    //     //     }
    //     // }
    //     // return mapData.initPostions[0].position;
    //     // switch (_mapType)
    //     // {
    //     //     case MapType.HeartGold:
    //     //         // return new Vector3(672.95f, 1, -384.97f);
    //     //         if(gen == "gen1") {
    //     //             return new Vector3(1024.27f, 1, -350.75f);
    //     //         } else { //if(gen == "gen2")
    //     //             return new Vector3(672.95f, 1, -384.97f);
    //     //         }
    //     //     case MapType.Platinum:
    //     //         return new Vector3(-1024, 0, 1024);
    //     //     case MapType.White:
    //     //         return new Vector3(-1024, 0, 1024);
    //     //     default:
    //     //         return new Vector3(-1024, 0, 1024);
    //     // }
    // }
    private void ConfigMap(IMapInfo mapInfo) {
        
        var envInfo = mapInfo.GetEnvInfo();
        if(envInfo != null) {
            MapController.Current.weatherMgr.SetCurrentTimeHours(envInfo.timeHour);
            MapController.Current.weatherMgr.SetMapWeather(envInfo.MapWeather);
        } else {
            MapController.Current.weatherMgr.SetMapWeather(WeatherEnumType.Normal);
        }
        string navMeshName = "NavMesh-" + mapInfo.GetMapNameId();
        _navMeshLoader.LoadNavMesh(navMeshName);
        _prefabRegionLoader = new PrefabRegionLoader(this.transform, mapInfo);
        _prefabRegionLoader.loadRadius = loadRadius;
        _waterMapChecker = new WaterMapChecker(mapInfo.GetMapData().waterMap);
        // if(_prefabRegionLoader == null) {
        // }
        // if(_waterMapChecker == null) {
        // }
        _npcLoader = new NPCLoader(
            this.transform,
            npcContainer,
            mapInfo.GetMapData().npcs
        );

        // 初始化事件对象加载器
        _mapEventObjectLoader = new MapEventObjectLoader(
            this.transform,
            mapInfo.GetMapData().runtimeObstacles,
            mapInfo.GetMapData().triggerEventComponents
        );
        _mapEventObjectLoader.SetLoadRadius(loadRadius);

        // LocalNPC.Init();
    }
    private void ClearMap() {
        // 清理事件对象加载器
        _mapEventObjectLoader?.ClearAllLoadedObjects();

        foreach (var child in this.transform.GetComponentsInChildren<Transform>()) {
            if(child == this.transform || child == this.npcContainer) continue;
            GameObject.Destroy(child.gameObject);
        }
        foreach (var child in npcContainer.GetComponentsInChildren<Transform>()) {
            if(child == npcContainer) continue;
            GameObject.Destroy(child.gameObject);
        }
    }
    public void LoadMap(Vector3 position)
    {
        if(_prefabRegionLoader == null) {
            Debug.LogError("PrefabRegionLoader is not initialized.");
            return;
        }
        _prefabRegionLoader.LoadNearby(position);
        _npcLoader.LoadNearbyNPCs(position, 5);
        _mapEventObjectLoader?.LoadNearbyEventObjects(position);
    }
    public async UniTask AsyncLoadMap(Vector3 position)
    {
        if(_prefabRegionLoader == null) {
            Debug.LogError("PrefabRegionLoader is not initialized.");
            return;
        }
        await _prefabRegionLoader.AsyncLoadNearby(position, 0);
        _npcLoader.LoadNearbyNPCs(position, 0);
        if (_mapEventObjectLoader != null)
        {
            await _mapEventObjectLoader.LoadNearbyEventObjectsAsync(position);
        }
    }

    public bool IsWater(Transform transform) {
        if(_waterMapChecker == null) {
            return false;
        }
        var position = this.transform.InverseTransformPoint(transform.position);
        // return _waterMapChecker.TryMoveToNavigableWaterCell(transform);
        return _waterMapChecker.IsWater(position);
    }

    public bool IsLand(Transform transform) {
        if(_waterMapChecker == null) {
            return true;
        }
        if (!NavMesh.SamplePosition(transform.position, out NavMeshHit hit, 0.1f, NavMesh.AllAreas))
        {
            return false;
        }
        var position = this.transform.InverseTransformPoint(transform.position);
        // return _waterMapChecker.TryMoveToNavigableWaterCell(transform);
        return _waterMapChecker.IsLand(position);
    }

    public WaterMapChecker.WaterInfo? GetWaterWorldPos(Transform transform) {
        if(_waterMapChecker == null) {
            return null;
        }
        var position = this.transform.InverseTransformPoint(transform.position);
        var info = _waterMapChecker.GetWaterWorldPos(position);
        if(info != null) {
            info.WordPos = this.transform.TransformPoint(info.WordPos);
        }
        return info;
    }
    // private void InitData(List<string> configFileNames)
    // {
    //     _prefabMap.Clear();

    //     foreach (var configFileName in configFileNames)
    //     {
    //         string fullPath = Path.Combine(Application.streamingAssetsPath, configFileName);
    //         if (!File.Exists(fullPath))
    //         {
    //             Debug.LogWarning("找不到配置文件: " + fullPath);
    //             continue;
    //         }

    //         string json = File.ReadAllText(fullPath);
    //         var list = JsonUtility.FromJson<PrefabDataList>(json);

    //         foreach (var item in list.data)
    //         {
    //             Vector3 pos = new Vector3(item.position[0], item.position[1], item.position[2]);
    //             _prefabMap[pos] = item; // 后者覆盖前者（相同位置）
    //         }
    //     }

    //     // _initialized = true;
    //     // Debug.Log($"Prefab map initialized with {_prefabMap.Count} entries.");
    // }

    // public void UnloadFar(Vector3 position)
    // {
    //     float unloadRadius = loadRadius * 1.5f;
    //     for (int i = _loadedInstances.Count - 1; i >= 0; i--)
    //     {
    //         var item = _loadedInstances[i];
    //         float dist = Vector3.Distance(item.position, position);
    //         if (dist > unloadRadius)
    //         {
    //             Destroy(item.instance);
    //             _loadedPositions.Remove(item.position);
    //             _loadedInstances.RemoveAt(i);

    //             if (debugLog)
    //                 Debug.Log($"Unloaded prefab at {item.position}");
    //         }
    //     }
    // }

    /// <summary>
    /// 获取传送点信息
    /// </summary>
    public MapTransmitPointInfoPack GetTransmitPointInfo(string transmitPointName)
    {
        if (_prefabRegionLoader == null) return null;
        return _prefabRegionLoader.GetTransmitPointInfo(transmitPointName);
    }

    /// <summary>
    /// 获取当前地图的所有传送点信息
    /// </summary>
    // public List<PrefabRegionLoader.PrefabTransmitMapInfo> GetCurrentMapTransmitPoints()
    // {
    //     if (_prefabRegionLoader == null) return new List<PrefabRegionLoader.PrefabTransmitMapInfo>();
    //     return _prefabRegionLoader.GetCurrentMapTransmitPoints();
    // }

    // /// <summary>
    // /// 获取目标地图的所有传送点信息
    // /// </summary>
    // public List<PrefabRegionLoader.PrefabTransmitMapInfo> GetTargetMapTransmitPoints(string targetMapName)
    // {
    //     if (_prefabRegionLoader == null) return new List<PrefabRegionLoader.PrefabTransmitMapInfo>();
    //     return _prefabRegionLoader.GetTargetMapTransmitPoints(targetMapName);
    // }

    /// <summary>
    /// 获取已加载的传送点对象
    /// </summary>
    public GameObject GetLoadedTransmitPoint(string transmitPointName)
    {
        if (_prefabRegionLoader == null) return null;
        return _prefabRegionLoader.GetLoadedTransmitPoint(transmitPointName);
    }

    /// <summary>
    /// 设置当前地图名称
    /// </summary>
    // public void SetCurrentMapName(string mapName)
    // {
    //     if (_prefabRegionLoader == null) return;
    //     _prefabRegionLoader.SetCurrentMapName(mapName);
    // }

    /// <summary>
    /// 获取已加载的运行时障碍物
    /// </summary>
    public GameObject GetLoadedObstacle(string obstacleName)
    {
        return _mapEventObjectLoader?.GetLoadedObstacle(obstacleName);
    }

    /// <summary>
    /// 获取已加载的触发事件
    /// </summary>
    public GameObject GetLoadedTriggerEvent(string eventName)
    {
        return _mapEventObjectLoader?.GetLoadedTriggerEvent(eventName);
    }

    /// <summary>
    /// 获取已加载的事件对象数量信息
    /// </summary>
    public (int obstacles, int triggerEvents) GetLoadedEventObjectCounts()
    {
        if (_mapEventObjectLoader == null) return (0, 0);
        return (_mapEventObjectLoader.GetLoadedObstacleCount(), _mapEventObjectLoader.GetLoadedTriggerEventCount());
    }

    /// <summary>
    /// 强制更新指定位置的事件对象（用于动态数据更新）
    /// </summary>
    public async UniTask ForceUpdateEventObjectsAtPosition(Vector3 worldPosition)
    {
        if (_mapEventObjectLoader != null)
        {
            await _mapEventObjectLoader.ForceUpdateAtPosition(worldPosition);
        }
    }
}