using System.Collections.Generic;
using UnityEngine;
using System.IO;
using Cysharp.Threading.Tasks;
using System.Threading;

/// <summary>
/// 区域预制体加载器，支持三维空间哈希，动态加载/卸载场景中的物体（包括传送点）
/// </summary>
public class PrefabRegionLoader
{

    // 所有预制体数据的原始坐标映射（Vector3 -> 数据）
    // private Dictionary<Vector3, PrefabData> _prefabMap = new();
    private Dictionary<Vector2Int, List<MapOtherUnitInfoPack>> _otherUnitInfoPackMap = new();
    private Dictionary<Vector2Int, MapBlock> _mainMapBlocks = new();

    // 三维空间哈希字典，key 为空间格子（cell），value 是该格子内的 prefab 列表
    // private Dictionary<Vector3Int, List<PrefabItem>> _spatialHashMap = new();

    // 已加载的位置集合
    private HashSet<Vector3> _loadedPositions = new();

    // 当前加载在场景中的实例列表
    private List<LoadedItem> _loadedInstances = new();

    // 缓存的附近格子中的所有 prefab 列表（避免重复查找）
    // private List<PrefabItem> _cachedNearbyPrefabs = new();

    // 地图挂载容器（通常是空 GameObject）
    private Transform _mapContainer;

    private string _regionName;
    private Vector3? _lastCenter = null;

    // 加载半径，单位是世界坐标（米）
    public float loadRadius = 70f;

    // 是否打印调试信息
    public bool debugLog = false;

    // 每个空间格子的大小（立方体边长），单位：米
    private readonly int _cellSize = 32;

    // 卸载半径，设为加载半径的 1.5 倍
    private float unloadRadius => loadRadius * 1.5f;

    // 所有传送点信息（根据 prefab 数据附带的结构）
    private Dictionary<string, MapTransmitPointInfoPack> _transmitPoints = new();

    // 当前场景中已生成的传送点对象（按名称索引）
    private Dictionary<string, GameObject> _loadedTransmitPoints = new();

    // 预制体缓存系统
    private Dictionary<string, GameObject> _prefabCache = new();
    private Dictionary<Vector2Int, GameObject> _loadedGridObjects = new();
    private HashSet<Vector2Int> _loadingGrids = new();
    private Queue<LoadTask> _loadQueue = new();

    // 预加载相关
    private Dictionary<string, GameObject> _preloadedPrefabs = new();
    private HashSet<string> _preloadingPrefabs = new();

    // 是否正在异步加载中
    private bool _isloading = false;
    private IMapInfo _mapInfo;
    private CancellationTokenSource _loadCancellationTokenSource;

    // 加载任务相关
    private bool _isProcessingQueue = false;
    private readonly int _maxConcurrentLoads = 3; // 最大并发加载数
    private int _currentLoadingCount = 0;
    /// <summary>
    /// 构造函数，初始化区域加载器
    /// </summary>
    public PrefabRegionLoader(Transform mapContainer, IMapInfo mapInfo)
    {
        _mapInfo = mapInfo;
        // regionName = "HeartGold"; // 示例强制赋值，可改为参数传入
        // _regionName = regionName;
        _mapContainer = mapContainer;
        // _configFileNames = configFileNames;
        InitData();
    }

    /// <summary>
    /// 加载给定位置附近的对象（支持动态卸载远处对象）
    /// </summary>
    public void LoadNearby(Vector3 worldPosition)
    {
        AsyncLoadNearby(worldPosition, 5).Forget();
    }

    /// <summary>
    /// 异步加载附近 prefab 的主逻辑函数
    /// </summary>
    public async UniTask AsyncLoadNearby(Vector3 worldPosition, int frame = 0)
    {
        if (_isloading) return;
        if (_mapContainer == null)
        {
            Debug.LogError("Map container is not set.");
            return;
        }

        _isloading = true;
        _loadCancellationTokenSource = new CancellationTokenSource();
        var token = _loadCancellationTokenSource.Token;

        try
        {
            // 转换为 local 坐标
            Vector3 localPosition = _mapContainer.InverseTransformPoint(worldPosition);
            _lastCenter = localPosition;

            // 获取附近格子信息
            var nearbyInfo = GetNearbyGridInfo(worldPosition, Mathf.CeilToInt(loadRadius / _cellSize));

            // 创建加载任务
            await CreateLoadTasks(nearbyInfo, token);

            // 开始处理加载队列
            if (!_isProcessingQueue)
            {
                ProcessLoadQueue(token).Forget();
            }

            // 卸载远处的对象
            UnloadFar(localPosition);

            if (debugLog)
            {
                Debug.Log($"AsyncLoadNearby completed at position: {worldPosition}");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in AsyncLoadNearby: {e.Message}");
        }
        finally
        {
            _isloading = false;
        }
    }
    public void CancelLoading()
    {
        if (_isloading)
        {
            _loadCancellationTokenSource?.Cancel();
            _loadCancellationTokenSource?.Dispose();
            _isloading = false;
        }
    }
    /// <summary>
    /// 卸载离当前位置太远的 prefab 实例
    /// </summary>
    private void UnloadFar(Vector3 localPos)
    {
        for (int i = _loadedInstances.Count - 1; i >= 0; i--)
        {
            var item = _loadedInstances[i];
            if (Vector3.Distance(localPos, item.position) > unloadRadius)
            {
                // 检查是否是传送点
                if (_loadedTransmitPoints.ContainsValue(item.instance))
                {
                    var transmitComponent = item.instance.GetComponent<TransmitMapComponent>();
                    if (transmitComponent != null)
                    {
                        _loadedTransmitPoints.Remove(transmitComponent.Name);
                    }
                }

                // 从网格对象字典中移除
                _loadedGridObjects.Remove(item.gridCoord);

                // 销毁实例
                GameObject.Destroy(item.instance);
                _loadedPositions.Remove(item.position);
                _loadedInstances.RemoveAt(i);

                if (debugLog)
                    Debug.Log($"♻️ Unloaded object at {item.position}, grid: {item.gridCoord}");
            }
        }
    }

    /// <summary>
    /// 清理预制体缓存
    /// </summary>
    public void ClearPrefabCache()
    {
        _prefabCache.Clear();
        _preloadedPrefabs.Clear();

        if (debugLog)
            Debug.Log("Prefab cache cleared");
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    public void LogCacheStats()
    {
        Debug.Log($"Cache Stats - Prefabs: {_prefabCache.Count}, Loaded Objects: {_loadedGridObjects.Count}, " +
                  $"Loading Grids: {_loadingGrids.Count}, Queue: {_loadQueue.Count}");
    }

    /// <summary>
    /// 智能加载：先预加载，再正常加载
    /// </summary>
    public async UniTask SmartLoadNearby(Vector3 worldPosition, int preloadDepth = 3)
    {
        // 先预加载更大范围的预制体
        await PreloadNearby(worldPosition, preloadDepth);

        // 再正常加载当前范围的对象
        await AsyncLoadNearby(worldPosition);

        if (debugLog)
        {
            Debug.Log($"Smart load completed for position: {worldPosition}");
        }
    }

    /// <summary>
    /// 检查指定格子是否已加载
    /// </summary>
    public bool IsGridLoaded(Vector2Int gridCoord)
    {
        return _loadedGridObjects.ContainsKey(gridCoord);
    }

    /// <summary>
    /// 获取指定格子的已加载对象
    /// </summary>
    public GameObject GetLoadedGridObject(Vector2Int gridCoord)
    {
        _loadedGridObjects.TryGetValue(gridCoord, out var obj);
        return obj;
    }

    /// <summary>
    /// 初始化数据：解析 JSON 配置、构建哈希表
    /// </summary>
    private void InitData()
    {
        _otherUnitInfoPackMap.Clear();
        _mainMapBlocks.Clear();
        var mapData = _mapInfo.GetMapData();
        if(mapData == null) {
            return;
        }
        if(mapData.otherUnits != null) {
            _otherUnitInfoPackMap = mapData.otherUnits;
        }
        if(mapData.mainBlocks != null) {
            _mainMapBlocks = mapData.mainBlocks;
        }
    }

    /// <summary>
    /// 将世界坐标转为二维格子坐标（只考虑x和z）
    /// </summary>
    private Vector2Int WorldToGridCoord(Vector3 worldPos)
    {
        return new Vector2Int(
            Mathf.RoundToInt(worldPos.x / _cellSize),
            Mathf.RoundToInt(worldPos.z / _cellSize)
        );
    }

    /// <summary>
    /// 根据世界坐标查找附近格子的信息
    /// </summary>
    /// <param name="worldPosition">世界坐标</param>
    /// <param name="searchDepth">搜索深度（格子距离），默认为2</param>
    /// <returns>包含附近格子信息的结果</returns>
    public NearbyGridInfo GetNearbyGridInfo(Vector3 worldPosition, int searchDepth = 2)
    {
        // 转换为本地坐标
        Vector3 localPosition = _mapContainer.InverseTransformPoint(worldPosition);

        // 转换为格子坐标
        Vector2Int centerGrid = WorldToGridCoord(localPosition);

        var result = new NearbyGridInfo();

        // 搜索周围的格子
        for (int x = centerGrid.x - searchDepth; x <= centerGrid.x + searchDepth; x++)
        {
            for (int z = centerGrid.y - searchDepth; z <= centerGrid.y + searchDepth; z++)
            {
                Vector2Int gridCoord = new Vector2Int(x, z);

                // 查找OtherUnit信息
                if (_otherUnitInfoPackMap.TryGetValue(gridCoord, out var otherUnit))
                {
                    result.otherUnits.Add(gridCoord, otherUnit);
                }

                // 查找MapBlock信息
                if (_mainMapBlocks.TryGetValue(gridCoord, out var mapBlock))
                {
                    result.mapBlocks.Add(gridCoord, mapBlock);
                }
            }
        }

        if (debugLog)
        {
            Debug.Log($"在坐标 {worldPosition} 附近找到 {result.otherUnits.Count} 个OtherUnit和 {result.mapBlocks.Count} 个MapBlock");
        }

        return result;
    }

    /// <summary>
    /// 创建加载任务
    /// </summary>
    private async UniTask CreateLoadTasks(NearbyGridInfo nearbyInfo, CancellationToken token)
    {
        // 处理OtherUnit
        foreach (var kvp in nearbyInfo.otherUnits)
        {
            var gridCoord = kvp.Key;
            var otherUnit = kvp.Value;

            if (_loadedGridObjects.ContainsKey(gridCoord) || _loadingGrids.Contains(gridCoord))
                continue;
            foreach (var unit in otherUnit)
            {
                if (!string.IsNullOrEmpty(unit.prefabAddress))
                {
                    var task = new LoadTask
                    {
                        gridCoord = gridCoord,
                        prefabAddress = unit.prefabAddress,
                        position = unit.position,
                        isOtherUnit = true,
                        otherUnitInfo = unit
                    };
                    _loadQueue.Enqueue(task);
                    _loadingGrids.Add(gridCoord);
                }
            }
            // if (!string.IsNullOrEmpty(otherUnit.prefabAddress))
            // {
            //     var task = new LoadTask
            //     {
            //         gridCoord = gridCoord,
            //         prefabAddress = otherUnit.prefabAddress,
            //         position = otherUnit.position,
            //         isOtherUnit = true,
            //         otherUnitInfo = otherUnit
            //     };

            //     _loadQueue.Enqueue(task);
            //     _loadingGrids.Add(gridCoord);
            // }
        }

        // 处理MapBlock
        foreach (var kvp in nearbyInfo.mapBlocks)
        {
            var gridCoord = kvp.Key;
            var mapBlock = kvp.Value;

            if (_loadedGridObjects.ContainsKey(gridCoord) || _loadingGrids.Contains(gridCoord))
                continue;

            if (!string.IsNullOrEmpty(mapBlock.prefabAddress))
            {
                var task = new LoadTask
                {
                    gridCoord = gridCoord,
                    prefabAddress = mapBlock.prefabAddress,
                    position = mapBlock.localPosition,
                    isOtherUnit = false,
                    mapBlockInfo = mapBlock
                };

                _loadQueue.Enqueue(task);
                _loadingGrids.Add(gridCoord);
            }
        }

        await UniTask.Yield(PlayerLoopTiming.Update, token);
    }

    /// <summary>
    /// 处理加载队列
    /// </summary>
    private async UniTask ProcessLoadQueue(CancellationToken token)
    {
        _isProcessingQueue = true;

        try
        {
            while (_loadQueue.Count > 0 && !token.IsCancellationRequested)
            {
                // 控制并发加载数量
                if (_currentLoadingCount >= _maxConcurrentLoads)
                {
                    await UniTask.Delay(50, cancellationToken: token);
                    continue;
                }

                var task = _loadQueue.Dequeue();
                LoadPrefabAsync(task, token).Forget();

                // 分帧处理，避免卡顿
                await UniTask.Yield(PlayerLoopTiming.Update, token);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in ProcessLoadQueue: {e.Message}");
        }
        finally
        {
            _isProcessingQueue = false;
        }
    }

    /// <summary>
    /// 异步加载单个预制体
    /// </summary>
    private async UniTask LoadPrefabAsync(LoadTask task, CancellationToken token)
    {
        _currentLoadingCount++;

        try
        {
            GameObject prefab = null;

            // 先检查缓存
            if (_prefabCache.TryGetValue(task.prefabAddress, out prefab))
            {
                await InstantiatePrefab(prefab, task, token);
            }
            else
            {
                // 异步加载预制体
                prefab = await AssertResourceLoader.Share.LoadAssetAsync<GameObject>(task.prefabAddress);
                if (prefab != null && !token.IsCancellationRequested)
                {
                    // 缓存预制体
                    _prefabCache[task.prefabAddress] = prefab;
                    await InstantiatePrefab(prefab, task, token);
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error loading prefab {task.prefabAddress}: {e.Message}");
        }
        finally
        {
            _currentLoadingCount--;
            _loadingGrids.Remove(task.gridCoord);
        }
    }

    /// <summary>
    /// 实例化预制体
    /// </summary>
    private async UniTask InstantiatePrefab(GameObject prefab, LoadTask task, CancellationToken token)
    {
        if (token.IsCancellationRequested) return;

        var objs = await GameObject.InstantiateAsync(prefab, _mapContainer).ToUniTask();
        if (objs == null || objs.Length == 0) return;

        GameObject instance = objs[0];
        instance.transform.SetParent(_mapContainer);
        instance.transform.localPosition = task.position;

        if (task.isOtherUnit)
        {
            // 处理OtherUnit
            instance.transform.localRotation = Quaternion.Euler(task.otherUnitInfo.rotation);
            instance.transform.localScale = task.otherUnitInfo.scale;
            instance.name = task.otherUnitInfo.name;
        }
        else
        {
            // 处理MapBlock
            instance.name = task.mapBlockInfo.blockName;
        }

        // 记录已加载的对象
        var loadedItem = new LoadedItem
        {
            instance = instance,
            position = task.position,
            gridCoord = task.gridCoord,
            prefabAddress = task.prefabAddress
        };

        _loadedInstances.Add(loadedItem);
        _loadedGridObjects[task.gridCoord] = instance;

        if (debugLog)
        {
            Debug.Log($"✅ Loaded: {task.prefabAddress} at grid {task.gridCoord}");
        }
    }

    /// <summary>
    /// 预加载指定位置附近的预制体
    /// </summary>
    public async UniTask PreloadNearby(Vector3 worldPosition, int preloadDepth = 3)
    {
        var nearbyInfo = GetNearbyGridInfo(worldPosition, preloadDepth);
        var prefabsToPreload = new HashSet<string>();

        // 收集需要预加载的预制体地址
        foreach (var otherUnit in nearbyInfo.otherUnits.Values)
        {
            foreach (var unit in otherUnit)
            {
                if (!string.IsNullOrEmpty(unit.prefabAddress))
                    prefabsToPreload.Add(unit.prefabAddress);
            }
        }

        foreach (var mapBlock in nearbyInfo.mapBlocks.Values)
        {
            if (!string.IsNullOrEmpty(mapBlock.prefabAddress))
                prefabsToPreload.Add(mapBlock.prefabAddress);
        }

        // 异步预加载
        var preloadTasks = new List<UniTask>();
        foreach (var prefabAddress in prefabsToPreload)
        {
            if (!_prefabCache.ContainsKey(prefabAddress) && !_preloadingPrefabs.Contains(prefabAddress))
            {
                preloadTasks.Add(PreloadSinglePrefab(prefabAddress));
            }
        }

        if (preloadTasks.Count > 0)
        {
            await UniTask.WhenAll(preloadTasks);
        }

        if (debugLog)
        {
            Debug.Log($"Preloaded {preloadTasks.Count} prefabs for position {worldPosition}");
        }
    }

    /// <summary>
    /// 预加载单个预制体
    /// </summary>
    private async UniTask PreloadSinglePrefab(string prefabAddress)
    {
        if (_preloadingPrefabs.Contains(prefabAddress)) return;

        _preloadingPrefabs.Add(prefabAddress);

        try
        {
            var prefab = await AssertResourceLoader.Share.LoadAssetAsync<GameObject>(prefabAddress);
            if (prefab != null)
            {
                _prefabCache[prefabAddress] = prefab;
                _preloadedPrefabs[prefabAddress] = prefab;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error preloading prefab {prefabAddress}: {e.Message}");
        }
        finally
        {
            _preloadingPrefabs.Remove(prefabAddress);
        }
    }

    public MapTransmitPointInfoPack GetTransmitPointInfo(string transmitPointName)
    {
        _transmitPoints.TryGetValue(transmitPointName, out var info);
        return info;
    }

    public GameObject GetLoadedTransmitPoint(string transmitPointName)
    {
        _loadedTransmitPoints.TryGetValue(transmitPointName, out var obj);
        return obj;
    }

    private class LoadedItem
    {
        public GameObject instance;
        public Vector3 position;
        public Vector2Int gridCoord;
        public string prefabAddress;
    }

    /// <summary>
    /// 加载任务类
    /// </summary>
    private class LoadTask
    {
        public Vector2Int gridCoord;
        public string prefabAddress;
        public Vector3 position;
        public bool isOtherUnit;
        public MapOtherUnitInfoPack otherUnitInfo;
        public MapBlock mapBlockInfo;
    }

    /// <summary>
    /// 附近格子信息的结果类
    /// </summary>
    public class NearbyGridInfo
    {
        public Dictionary<Vector2Int, List<MapOtherUnitInfoPack>> otherUnits = new Dictionary<Vector2Int, List<MapOtherUnitInfoPack>>();
        public Dictionary<Vector2Int, MapBlock> mapBlocks = new Dictionary<Vector2Int, MapBlock>();
    }
}