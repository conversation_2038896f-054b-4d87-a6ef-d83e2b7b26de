using System.Collections.Generic;
using UnityEngine;
using System.IO;
using Cysharp.Threading.Tasks;
using System.Threading;
using System.Linq;

/// <summary>
/// 格子类型枚举
/// </summary>
public enum GridType
{
    Normal,    // 正式加载的格子
    Preload    // 预加载的格子
}

/// <summary>
/// 预制体缓存项（LRU缓存）
/// </summary>
public class PrefabCacheItem
{
    public GameObject prefab;
    public float lastAccessTime;
    public int accessCount;

    public PrefabCacheItem(GameObject prefab)
    {
        this.prefab = prefab;
        this.lastAccessTime = Time.time;
        this.accessCount = 1;
    }

    public void Access()
    {
        lastAccessTime = Time.time;
        accessCount++;
    }
}

/// <summary>
/// 区域预制体加载器，支持三维空间哈希，动态加载/卸载场景中的物体（包括传送点）
/// </summary>
public class PrefabRegionLoader
{

    // 所有预制体数据的原始坐标映射（Vector3 -> 数据）
    // private Dictionary<Vector3, PrefabData> _prefabMap = new();
    private Dictionary<Vector2Int, List<MapOtherUnitInfoPack>> _otherUnitInfoPackMap = new();
    private Dictionary<Vector2Int, MapBlock> _mainMapBlocks = new();

    // 三维空间哈希字典，key 为空间格子（cell），value 是该格子内的 prefab 列表
    // private Dictionary<Vector3Int, List<PrefabItem>> _spatialHashMap = new();

    // 格子对象管理：每个格子坐标对应的所有GameObject列表
    private Dictionary<Vector2Int, List<GameObject>> _gridObjects = new();
    private Dictionary<Vector2Int, List<GameObject>> _gridToPreloadObjects = new();
    // 对象到格子的反向映射，用于快速查找对象属于哪个格子
    // private Dictionary<GameObject, Vector2Int> _objectToGrid = new();

    // 格子类型标记：区分正式加载和预加载的格子
    // private Dictionary<Vector2Int, GridType> _gridTypes = new();

    // 预加载组管理：格子坐标到预加载组ID的映射
    // private Dictionary<Vector2Int, string> _gridToPreloadGroup = new();

    // 地图挂载容器（通常是空 GameObject）
    private Transform _mapContainer;

    // 加载深度（格子数量）
    public int loadDepth = 2;

    // 卸载深度，比加载深度多1
    public int unloadDepth => loadDepth + 1;

    // 是否打印调试信息
    public bool debugLog = false;

    // 每个空间格子的大小（立方体边长），单位：米
    private readonly int _cellSize = 32;

    // 性能优化：卸载检查间隔（帧数）
    private int _unloadCheckInterval = 30; // 每30帧检查一次卸载
    private int _frameCounter = 0;
    private Vector2Int _lastUnloadCenter = Vector2Int.zero;

    // 所有传送点信息（根据 prefab 数据附带的结构）
    private Dictionary<string, MapTransmitPointInfoPack> _transmitPoints = new();

    // 当前场景中已生成的传送点对象（按名称索引）
    private Dictionary<string, GameObject> _loadedTransmitPoints = new();

    // LRU预制体缓存系统
    private Dictionary<string, PrefabCacheItem> _prefabCache = new();
    private int _prefabCacheLimit = 100; // 缓存限制
    private int _cacheCheckInterval = 300; // 缓存检查间隔（帧数）
    private int _cacheFrameCounter = 0;

    // 加载任务管理
    private HashSet<Vector2Int> _loadingGrids = new();
    private Queue<LoadTask> _loadQueue = new();

    // 预加载组管理
    // private Dictionary<string, PreloadGroup> _preloadGroups = new();
    // private HashSet<string> _preloadingPrefabs = new();

    // 是否正在异步加载中
    private bool _isloading = false;
    private IMapInfo _mapInfo;
    private CancellationTokenSource _loadCancellationTokenSource;
    private CancellationTokenSource _preloadCancellationTokenSource;

    // 加载任务相关
    private bool _isProcessingQueue = false;
    private readonly int _maxConcurrentLoads = 3; // 最大并发加载数
    private int _currentLoadingCount = 0;

    // 回调事件
    public System.Action<Vector3, bool> OnLoadCompleted; // 位置, 是否成功
    public System.Action<string, bool> OnPreloadCompleted; // 预加载组名, 是否成功
    /// <summary>
    /// 构造函数，初始化区域加载器
    /// </summary>
    public PrefabRegionLoader(Transform mapContainer, IMapInfo mapInfo)
    {
        _mapInfo = mapInfo;
        // regionName = "HeartGold"; // 示例强制赋值，可改为参数传入
        // _regionName = regionName;
        _mapContainer = mapContainer;
        // _configFileNames = configFileNames;
        InitData();
    }

    /// <summary>
    /// 加载给定位置附近的对象（支持动态卸载远处对象）
    /// </summary>
    public void LoadNearby(Vector3 worldPosition)
    {
        AsyncLoadNearby(worldPosition).Forget();
    }
    public async UniTask AsyncLoadNearby(Vector3 worldPosition) {
        if (_isloading) return;
        if (_mapContainer == null)
        {
            Debug.LogError("Map container is not set.");
            return;
        }

        _isloading = true;
        _loadCancellationTokenSource = new CancellationTokenSource();
        var token = _loadCancellationTokenSource.Token;
        await AsyncLoadNearby(worldPosition, false, token);
    }

    /// <summary>
    /// 异步加载附近 prefab 的主逻辑函数
    /// </summary>
    private async UniTask AsyncLoadNearby(Vector3 worldPosition, bool isPreload, CancellationToken token)
    {
        try
        {
            // 转换为 local 坐标
            Vector3 localPosition = _mapContainer.InverseTransformPoint(worldPosition);

            // 转换为格子坐标
            Vector2Int centerGrid = WorldToGridCoord(localPosition);

            if(_gridToPreloadObjects.ContainsKey(centerGrid)) {
                var preloadObjs = _gridToPreloadObjects[centerGrid];
                foreach (var obj in preloadObjs) {
                    var transmit = obj.GetComponent<TransmitMapComponent>();
                    if(transmit != null && _gridObjects.ContainsKey(centerGrid)) {
                        _gridToPreloadObjects.Remove(centerGrid);
                    }
                }
                GetTransmitPointInfo()
                //TODO 需要遍历目标位置是不是有传送点，如果有则需要正式加载的位置变更为预加载位置
                //传送的深度不能超过1 就是只预加载当前正式加载数据中的传送点位置
            }
            // 获取附近格子信息
            var nearbyInfo = GetNearbyGridInfo(localPosition, loadDepth);

            // 创建加载任务
            await CreateLoadTasks(nearbyInfo, isPreload, token);

            // 开始处理加载队列
            if (!_isProcessingQueue)
            {
                ProcessLoadQueue(token).Forget();
            }
            if(!isPreload) {
                // 卸载远处的对象
                UnloadFar(localPosition);
            }

            if (debugLog)
            {
                Debug.Log($"AsyncLoadNearby completed at position: {worldPosition}");
            }

            // 触发加载完成回调
            OnLoadCompleted?.Invoke(worldPosition, true);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in AsyncLoadNearby: {e.Message}");
            OnLoadCompleted?.Invoke(worldPosition, false);
        }
        finally
        {
            _isloading = false;
        }
    }
    public void CancelLoading()
    {
        if (_isloading)
        {
            _loadCancellationTokenSource?.Cancel();
            _loadCancellationTokenSource?.Dispose();
            _isloading = false;
        }
    }
    /// <summary>
    /// 卸载离当前位置太远的正式加载对象（基于格子距离，性能优化）
    /// </summary>
    private void UnloadFar(Vector3 localPos)
    {
        // 性能优化：不是每帧都检查卸载
        _frameCounter++;
        if (_frameCounter < _unloadCheckInterval)
            return;

        _frameCounter = 0;

        // 转换为格子坐标
        Vector2Int currentGrid = WorldToGridCoord(localPos);

        // 性能优化：如果玩家没有移动太远，跳过检查
        if (Vector2Int.Distance(currentGrid, _lastUnloadCenter) < 1)
            return;

        _lastUnloadCenter = currentGrid;

        // 使用格子距离进行卸载检查
        UnloadByGridDistance(_gridObjects, currentGrid);
    }

    /// <summary>
    /// 基于格子距离卸载对象
    /// </summary>
    private void UnloadByGridDistance(Dictionary<Vector2Int, List<GameObject>> gridObjects, Vector2Int centerGrid)
    {
        // var gridsToUnload = new List<Vector2Int>();

        // 收集需要卸载的格子
        //修改循环，内部会删除key

        foreach (var grid in gridObjects)
        {
            // 跳过预加载格子，它们由预加载组管理
            // if (_gridTypes.TryGetValue(gridCoord, out var gridType) && gridType == GridType.Preload)
            //     continue;
            // if(!_gridObjects.ContainsKey(gridCoord))
            // {
            //     return
            // }
            var gridCoord = grid.Key;
            // 计算格子距离
            int gridDistance = Mathf.Max(
                Mathf.Abs(gridCoord.x - centerGrid.x),
                Mathf.Abs(gridCoord.y - centerGrid.y)
            );

            // 如果超出卸载深度，标记为需要卸载
            if (gridDistance > unloadDepth && grid.Value.Count > 0)
            {
                // gridsToUnload.Add(gridCoord);
                UnloadGridObjects(grid.Value);
                gridObjects.Remove(gridCoord);
            }
        }

        // 批量卸载格子
        // if (gridsToUnload.Count > 0)
        // {
        //     foreach (var gridCoord in gridsToUnload)
        //     {
        //         UnloadGrid(gridCoord);
        //     }

        //     if (debugLog)
        //     {
        //         Debug.Log($"♻️ 批量卸载了 {gridsToUnload.Count} 个格子，当前格子: {centerGrid}");
        //     }
        // }
    }

    /// <summary>
    /// 卸载整个格子的所有对象
    /// </summary>
    private void UnloadGridObjects(List<GameObject> objects)
    {
        // if (!_gridObjects.TryGetValue(gridCoord, out var objects))
        //     return;

        // 销毁格子中的所有对象
        foreach (var obj in objects)
        {
            if (obj != null)
            {
                // 检查是否是传送点
                if (_loadedTransmitPoints.ContainsValue(obj))
                {
                    var transmitComponent = obj.GetComponent<TransmitMapComponent>();
                    if (transmitComponent != null)
                    {
                        _loadedTransmitPoints.Remove(transmitComponent.Name);
                    }
                    if(!string.IsNullOrEmpty(transmitComponent.ToMapName)) {
                        var toTransmitPoint = GetTransmitPointInfo(transmitComponent.ToMapName);
                        var gridCoord = WorldToGridCoord(toTransmitPoint.position);
                        if(_gridToPreloadObjects.ContainsKey(gridCoord)) {
                            //卸载预加载组件
                            UnloadGridObjects(_gridToPreloadObjects[gridCoord]);
                        }
                    }
                }
                
                // 从反向映射中移除
                // _objectToGrid.Remove(obj);

                // 销毁对象
                GameObject.Destroy(obj);
            }
        }

        // 清理格子相关数据
        // _gridObjects.Remove(gridCoord);
        // _gridTypes.Remove(gridCoord);
        // _gridToPreloadGroup.Remove(gridCoord);

        // if (debugLog)
        // {
        //     Debug.Log($"♻️ 卸载格子 {gridCoord}，包含 {objects.Count} 个对象");
        // }
    }



    /// <summary>
    /// 清理预制体缓存
    /// </summary>
    public void ClearPrefabCache()
    {
        _prefabCache.Clear();

        if (debugLog)
            Debug.Log("Prefab cache cleared");
    }

    /// <summary>
    /// LRU缓存清理（自动调用）
    /// </summary>
    private void CleanupPrefabCache()
    {
        if (_prefabCache.Count <= _prefabCacheLimit)
            return;

        // 按最后访问时间排序，移除最旧的缓存项
        var sortedItems = _prefabCache.OrderBy(kvp => kvp.Value.lastAccessTime).ToList();
        int removeCount = _prefabCache.Count - _prefabCacheLimit;

        for (int i = 0; i < removeCount; i++)
        {
            _prefabCache.Remove(sortedItems[i].Key);
        }

        if (debugLog)
        {
            Debug.Log($"LRU缓存清理: 移除了 {removeCount} 个预制体，当前缓存: {_prefabCache.Count}");
        }
    }

    /// <summary>
    /// 清理指定预加载组
    /// </summary>
    // public void ClearPreloadGroup(string groupId)
    // {
    //     if (_preloadGroups.TryGetValue(groupId, out var group))
    //     {
    //         // 找到该组的所有格子并卸载
    //         var gridsToUnload = new List<Vector2Int>();
    //         foreach (var kvp in _gridToPreloadGroup)
    //         {
    //             if (kvp.Value == groupId)
    //             {
    //                 gridsToUnload.Add(kvp.Key);
    //             }
    //         }

    //         foreach (var gridCoord in gridsToUnload)
    //         {
    //             UnloadGrid(gridCoord);
    //         }

    //         // 清理预加载组
    //         _preloadGroups.Remove(groupId);

    //         if (debugLog)
    //             Debug.Log($"Cleared preload group: {groupId}, unloaded {gridsToUnload.Count} grids");
    //     }
    // }

    /// <summary>
    /// 清理所有预加载组
    /// </summary>
    // public void ClearAllPreloadGroups()
    // {
    //     var groupIds = new List<string>(_preloadGroups.Keys);
    //     foreach (var groupId in groupIds)
    //     {
    //         ClearPreloadGroup(groupId);
    //     }
    // }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    public void LogCacheStats()
    {
        // int totalObjects = _gridObjects.Values.Sum(list => list.Count);
        // Debug.Log($"Cache Stats - Prefabs: {_prefabCache.Count}, Grid Objects: {totalObjects}, " +
        //           $"Loaded Grids: {_gridObjects.Count}, Loading Grids: {_loadingGrids.Count}, " +
        //           $"Queue: {_loadQueue.Count}, Preload Groups: {_preloadGroups.Count}");
    }

    /// <summary>
    /// 设置卸载检查间隔（性能调优）
    /// </summary>
    /// <param name="interval">检查间隔帧数，越大性能越好但响应越慢</param>
    public void SetUnloadCheckInterval(int interval)
    {
        _unloadCheckInterval = Mathf.Max(1, interval);
        if (debugLog)
        {
            Debug.Log($"卸载检查间隔设置为: {_unloadCheckInterval} 帧");
        }
    }

    /// <summary>
    /// 强制执行一次卸载检查
    /// </summary>
    // public void ForceUnloadCheck(Vector3 worldPosition)
    // {
    //     Vector3 localPosition = _mapContainer.InverseTransformPoint(worldPosition);
    //     Vector2Int currentGrid = WorldToGridCoord(localPosition);

    //     _frameCounter = 0; // 重置计数器
    //     _lastUnloadCenter = currentGrid;

    //     UnloadByGridDistance(currentGrid);

    //     if (debugLog)
    //     {
    //         Debug.Log($"强制执行卸载检查，当前格子: {currentGrid}");
    //     }
    // }

    /// <summary>
    /// 获取当前加载状态信息
    /// </summary>
    // public (int totalLoaded, int normalLoaded, int preloaded) GetLoadedStats()
    // {
    //     int normalLoaded = 0;
    //     int preloaded = 0;

    //     foreach (var kvp in _gridTypes)
    //     {
    //         if (kvp.Value == GridType.Normal)
    //             normalLoaded += _gridObjects.TryGetValue(kvp.Key, out var normalObjs) ? normalObjs.Count : 0;
    //         else if (kvp.Value == GridType.Preload)
    //             preloaded += _gridObjects.TryGetValue(kvp.Key, out var preloadObjs) ? preloadObjs.Count : 0;
    //     }

    //     return (normalLoaded + preloaded, normalLoaded, preloaded);
    // }

    /// <summary>
    /// 智能加载：先预加载，再正常加载
    /// </summary>
    // public async UniTask SmartLoadNearby(Vector3 worldPosition, int preloadDepth = 3)
    // {
    //     // 先预加载更大范围的预制体
    //     await PreloadNearby(worldPosition, preloadDepth);

    //     // 再正常加载当前范围的对象
    //     await AsyncLoadNearby(worldPosition);

    //     if (debugLog)
    //     {
    //         Debug.Log($"Smart load completed for position: {worldPosition}");
    //     }
    // }

    /// <summary>
    /// 检查指定格子是否已加载
    /// </summary>
    public bool IsGridLoaded(Vector2Int gridCoord)
    {
        return _gridObjects.ContainsKey(gridCoord);
    }

    /// <summary>
    /// 获取指定格子的已加载对象
    /// </summary>
    public List<GameObject> GetLoadedGridObjects(Vector2Int gridCoord)
    {
        _gridObjects.TryGetValue(gridCoord, out var objects);
        return objects ?? new List<GameObject>();
    }

    /// <summary>
    /// 设置预制体缓存限制
    /// </summary>
    public void SetPrefabCacheLimit(int limit)
    {
        _prefabCacheLimit = Mathf.Max(10, limit);
        if (debugLog)
        {
            Debug.Log($"预制体缓存限制设置为: {_prefabCacheLimit}");
        }
    }

    /// <summary>
    /// 获取指定格子的对象数量
    /// </summary>
    public int GetGridObjectCount(Vector2Int gridCoord)
    {
        return _gridObjects.TryGetValue(gridCoord, out var objects) ? objects.Count : 0;
    }

    /// <summary>
    /// 获取所有已加载格子的坐标
    /// </summary>
    public List<Vector2Int> GetAllLoadedGrids()
    {
        return new List<Vector2Int>(_gridObjects.Keys);
    }

    /// <summary>
    /// 检查预加载组是否存在
    /// </summary>
    // public bool HasPreloadGroup(string groupId)
    // {
    //     return _preloadGroups.ContainsKey(groupId);
    // }

    /// <summary>
    /// 获取预加载组信息
    /// </summary>
    // public (int gridCount, Vector3 centerPosition) GetPreloadGroupInfo(string groupId)
    // {
    //     if (_preloadGroups.TryGetValue(groupId, out var group))
    //     {
    //         return (group.preloadedGrids.Count, group.centerPosition);
    //     }
    //     return (0, Vector3.zero);
    // }

    /// <summary>
    /// 激活/停用预加载组（显示/隐藏对象）
    /// </summary>
    // public void SetPreloadGroupActive(string groupId, bool active)
    // {
    //     if (_preloadGroups.TryGetValue(groupId, out var group))
    //     {
    //         group.isActive = active;
    //         foreach (var gridCoord in group.preloadedGrids)
    //         {
    //             if (_gridObjects.TryGetValue(gridCoord, out var objects))
    //             {
    //                 foreach (var obj in objects)
    //                 {
    //                     if (obj != null)
    //                     {
    //                         obj.SetActive(active);
    //                     }
    //                 }
    //             }
    //         }

    //         if (debugLog)
    //         {
    //             Debug.Log($"预加载组 {groupId} 设置为 {(active ? "激活" : "停用")}");
    //         }
    //     }
    // }

    /// <summary>
    /// 初始化数据：解析 JSON 配置、构建哈希表
    /// </summary>
    private void InitData()
    {
        _otherUnitInfoPackMap.Clear();
        _mainMapBlocks.Clear();
        var mapData = _mapInfo.GetMapData();
        if(mapData == null) {
            return;
        }
        if(mapData.otherUnits != null) {
            _otherUnitInfoPackMap = mapData.otherUnits;
        }
        if(mapData.mainBlocks != null) {
            _mainMapBlocks = mapData.mainBlocks;
        }
    }

    /// <summary>
    /// 将世界坐标转为二维格子坐标（只考虑x和z）
    /// </summary>
    private Vector2Int WorldToGridCoord(Vector3 worldPos)
    {
        return new Vector2Int(
            Mathf.RoundToInt(worldPos.x / _cellSize),
            Mathf.RoundToInt(worldPos.z / _cellSize)
        );
    }

    /// <summary>
    /// 根据local坐标查找附近格子的信息
    /// </summary>
    /// <param name="localPosition">local坐标</param>
    /// <param name="searchDepth">搜索深度（格子距离），默认为2</param>
    /// <returns>包含附近格子信息的结果</returns>
    public NearbyGridInfo GetNearbyGridInfo(Vector3 localPosition, int searchDepth = 2)
    {
        // 转换为本地坐标
        // Vector3 localPosition = _mapContainer.InverseTransformPoint(worldPosition);

        // 转换为格子坐标
        Vector2Int centerGrid = WorldToGridCoord(localPosition);

        var result = new NearbyGridInfo();

        // 搜索周围的格子
        for (int x = centerGrid.x - searchDepth; x <= centerGrid.x + searchDepth; x++)
        {
            for (int z = centerGrid.y - searchDepth; z <= centerGrid.y + searchDepth; z++)
            {
                Vector2Int gridCoord = new Vector2Int(x, z);

                // 查找OtherUnit信息
                if (_otherUnitInfoPackMap.TryGetValue(gridCoord, out var otherUnit))
                {
                    result.otherUnits.Add(gridCoord, otherUnit);
                }

                // 查找MapBlock信息
                if (_mainMapBlocks.TryGetValue(gridCoord, out var mapBlock))
                {
                    result.mapBlocks.Add(gridCoord, mapBlock);
                }
            }
        }

        // if (debugLog)
        // {
        //     Debug.Log($"在坐标 {worldPosition} 附近找到 {result.otherUnits.Count} 个OtherUnit和 {result.mapBlocks.Count} 个MapBlock");
        // }

        return result;
    }

    /// <summary>
    /// 创建加载任务
    /// </summary>
    private async UniTask CreateLoadTasks(NearbyGridInfo nearbyInfo, bool isPreload, CancellationToken token)
    {
        var gridObjects = isPreload ? _gridToPreloadObjects : _gridObjects;
        // 处理OtherUnit
        foreach (var kvp in nearbyInfo.otherUnits)
        {
            var gridCoord = kvp.Key;
            var otherUnit = kvp.Value;

            if (gridObjects.ContainsKey(gridCoord) || _loadingGrids.Contains(gridCoord))
                continue;
            foreach (var unit in otherUnit)
            {
                if (!string.IsNullOrEmpty(unit.prefabAddress))
                {
                    var task = new LoadTask
                    {
                        gridCoord = gridCoord,
                        prefabAddress = unit.prefabAddress,
                        position = unit.position,
                        isOtherUnit = true,
                        isPreload = isPreload,
                        otherUnitInfo = unit
                    };
                    _loadQueue.Enqueue(task);
                    _loadingGrids.Add(gridCoord);
                }
            }
        }

        // 处理MapBlock
        foreach (var kvp in nearbyInfo.mapBlocks)
        {
            var gridCoord = kvp.Key;
            var mapBlock = kvp.Value;

            if (_gridObjects.ContainsKey(gridCoord) || _loadingGrids.Contains(gridCoord))
                continue;

            if (!string.IsNullOrEmpty(mapBlock.prefabAddress))
            {
                var task = new LoadTask
                {
                    gridCoord = gridCoord,
                    prefabAddress = mapBlock.prefabAddress,
                    position = mapBlock.localPosition,
                    isOtherUnit = false,
                    isPreload = isPreload,
                    mapBlockInfo = mapBlock
                };

                _loadQueue.Enqueue(task);
                _loadingGrids.Add(gridCoord);
            }
        }

        await UniTask.Yield(PlayerLoopTiming.Update, token);
    }

    /// <summary>
    /// 处理加载队列
    /// </summary>
    private async UniTask ProcessLoadQueue(CancellationToken token)
    {
        _isProcessingQueue = true;

        try
        {
            while (_loadQueue.Count > 0 && !token.IsCancellationRequested)
            {
                // 控制并发加载数量
                if (_currentLoadingCount >= _maxConcurrentLoads)
                {
                    await UniTask.Delay(50, cancellationToken: token);
                    continue;
                }

                var task = _loadQueue.Dequeue();
                LoadPrefabAsync(task, token).Forget();

                // 分帧处理，避免卡顿
                await UniTask.Yield(PlayerLoopTiming.Update, token);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in ProcessLoadQueue: {e.Message}");
        }
        finally
        {
            _isProcessingQueue = false;
        }
    }

    /// <summary>
    /// 异步加载单个预制体
    /// </summary>
    private async UniTask LoadPrefabAsync(LoadTask task, CancellationToken token)
    {
        _currentLoadingCount++;

        try
        {
            GameObject prefab = await GetOrLoadPrefab(task.prefabAddress);
            if (prefab != null && !token.IsCancellationRequested)
            {
                await InstantiatePrefab(prefab, task, token);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error loading prefab {task.prefabAddress}: {e.Message}");
        }
        finally
        {
            _currentLoadingCount--;
            _loadingGrids.Remove(task.gridCoord);
        }
    }

    /// <summary>
    /// 获取或加载预制体（带LRU缓存）
    /// </summary>
    private async UniTask<GameObject> GetOrLoadPrefab(string prefabAddress)
    {
        // 检查缓存
        if (_prefabCache.TryGetValue(prefabAddress, out var cacheItem))
        {
            // 更新访问时间
            cacheItem.Access();
            return cacheItem.prefab;
        }

        // 异步加载
        var prefab = await AssertResourceLoader.Share.LoadAssetAsync<GameObject>(prefabAddress);
        if (prefab != null)
        {
            // 添加到缓存
            _prefabCache[prefabAddress] = new PrefabCacheItem(prefab);

            // 检查缓存清理
            CheckAndCleanupCache();
        }

        return prefab;
    }

    /// <summary>
    /// 检查并清理缓存
    /// </summary>
    private void CheckAndCleanupCache()
    {
        _cacheFrameCounter++;
        if (_cacheFrameCounter >= _cacheCheckInterval)
        {
            _cacheFrameCounter = 0;
            CleanupPrefabCache();
        }
    }

    /// <summary>
    /// 实例化预制体到格子
    /// </summary>
    private async UniTask InstantiatePrefab(GameObject prefab, LoadTask task, CancellationToken token)
    {
        if (token.IsCancellationRequested) return;

        var objs = await GameObject.InstantiateAsync(prefab, _mapContainer).ToUniTask();
        if (objs == null || objs.Length == 0) return;

        GameObject instance = objs[0];
        instance.transform.SetParent(_mapContainer);
        instance.transform.localPosition = task.position;
        var gridObjects = task.isPreload ? _gridToPreloadObjects : _gridObjects;
        if (task.isOtherUnit)
        {
            // 处理OtherUnit
            instance.transform.localRotation = Quaternion.Euler(task.otherUnitInfo.rotation);
            instance.transform.localScale = task.otherUnitInfo.scale;
            instance.name = task.otherUnitInfo.name;
            if(!task.isPreload && task.otherUnitInfo.transmitMapInfo != null && 
            !string.IsNullOrEmpty(task.otherUnitInfo.transmitMapInfo.toMapName)) {
                //预加载传送点目标位置的资源
                var transmitPoint = GetTransmitPointInfo(task.otherUnitInfo.transmitMapInfo.toMapName);
                AsyncLoadNearby(transmitPoint.position, true, default).Forget();
            }
        }
        else
        {
            // 处理MapBlock
            instance.name = task.mapBlockInfo.blockName;
        }
    
        // 添加到格子管理系统
        if (!gridObjects.ContainsKey(task.gridCoord))
        {
            gridObjects[task.gridCoord] = new List<GameObject>();
        }
        gridObjects[task.gridCoord].Add(instance);

        if (debugLog)
        {
            Debug.Log($"✅ Loaded: {task.prefabAddress} at grid {task.gridCoord}");
        }
    }

    /// <summary>
    /// 添加对象到格子管理系统
    /// </summary>
    // private void AddObjectToGrid(GameObject obj, Vector2Int gridCoord, GridType gridType, string preloadGroupId = null)
    // {
    //     // 添加到格子对象列表
    //     if (!_gridObjects.ContainsKey(gridCoord))
    //     {
    //         _gridObjects[gridCoord] = new List<GameObject>();
    //     }
    //     _gridObjects[gridCoord].Add(obj);

    //     // 添加反向映射
    //     _objectToGrid[obj] = gridCoord;

    //     // 设置格子类型
    //     _gridTypes[gridCoord] = gridType;

    //     // 如果是预加载，记录预加载组
    //     if (gridType == GridType.Preload && !string.IsNullOrEmpty(preloadGroupId))
    //     {
    //         _gridToPreloadGroup[gridCoord] = preloadGroupId;
    //     }
    // }

    /// <summary>
    /// 预加载指定位置附近的预制体
    /// </summary>
    // public async UniTask PreloadNearby(Vector3 worldPosition, int preloadDepth = 3)
    // {
    //     var nearbyInfo = GetNearbyGridInfo(worldPosition, preloadDepth);
    //     var prefabsToPreload = new HashSet<string>();

    //     // 收集需要预加载的预制体地址
    //     foreach (var otherUnit in nearbyInfo.otherUnits.Values)
    //     {
    //         foreach (var unit in otherUnit)
    //         {
    //             if (!string.IsNullOrEmpty(unit.prefabAddress))
    //                 prefabsToPreload.Add(unit.prefabAddress);
    //         }
    //     }

    //     foreach (var mapBlock in nearbyInfo.mapBlocks.Values)
    //     {
    //         if (!string.IsNullOrEmpty(mapBlock.prefabAddress))
    //             prefabsToPreload.Add(mapBlock.prefabAddress);
    //     }

    //     // 异步预加载
    //     var preloadTasks = new List<UniTask>();
    //     foreach (var prefabAddress in prefabsToPreload)
    //     {
    //         if (!_prefabCache.ContainsKey(prefabAddress) && !_preloadingPrefabs.Contains(prefabAddress))
    //         {
    //             preloadTasks.Add(PreloadSinglePrefab(prefabAddress));
    //         }
    //     }

    //     if (preloadTasks.Count > 0)
    //     {
    //         await UniTask.WhenAll(preloadTasks);
    //     }

    //     if (debugLog)
    //     {
    //         Debug.Log($"Preloaded {preloadTasks.Count} prefabs for position {worldPosition}");
    //     }
    // }

    // /// <summary>
    // /// 预加载单个预制体
    // /// </summary>
    // private async UniTask PreloadSinglePrefab(string prefabAddress)
    // {
    //     if (_preloadingPrefabs.Contains(prefabAddress)) return;

    //     _preloadingPrefabs.Add(prefabAddress);

    //     try
    //     {
    //         // 使用统一的缓存方法
    //         await GetOrLoadPrefab(prefabAddress);
    //     }
    //     catch (System.Exception e)
    //     {
    //         Debug.LogError($"Error preloading prefab {prefabAddress}: {e.Message}");
    //     }
    //     finally
    //     {
    //         _preloadingPrefabs.Remove(prefabAddress);
    //     }
    // }

    public MapTransmitPointInfoPack GetTransmitPointInfo(string transmitPointName)
    {
        _transmitPoints.TryGetValue(transmitPointName, out var info);
        return info;
    }

    public GameObject GetLoadedTransmitPoint(string transmitPointName)
    {
        _loadedTransmitPoints.TryGetValue(transmitPointName, out var obj);
        return obj;
    }

    /// <summary>
    /// 为传送点预加载指定位置的对象（实际实例化到场景中）
    /// </summary>
    /// <param name="groupId">预加载组ID（通常是传送点名称）</param>
    /// <param name="targetPosition">目标位置</param>
    /// <param name="preloadDepth">预加载深度</param>
    // public async UniTask PreloadForTeleport(string groupId, Vector3 targetPosition, int preloadDepth = 2)
    // {
    //     try
    //     {
    //         // 如果组已存在，先清理
    //         if (_preloadGroups.ContainsKey(groupId))
    //         {
    //             ClearPreloadGroup(groupId);
    //         }

    //         // 创建新的预加载组
    //         var group = new PreloadGroup(groupId, targetPosition);
    //         _preloadGroups[groupId] = group;

    //         // 获取目标位置附近的格子信息
    //         var nearbyInfo = GetNearbyGridInfo(targetPosition, preloadDepth);

    //         // 预加载并实例化对象
    //         await PreloadAndInstantiateObjects(group, nearbyInfo);

    //         // 触发回调
    //         OnPreloadCompleted?.Invoke(groupId, true);

    //         if (debugLog)
    //         {
    //             Debug.Log($"✅ 传送点预加载完成: {groupId}, 格子数量: {group.preloadedGrids.Count}");
    //         }
    //     }
    //     catch (System.Exception e)
    //     {
    //         Debug.LogError($"传送点预加载失败 {groupId}: {e.Message}");
    //         OnPreloadCompleted?.Invoke(groupId, false);
    //     }
    // }

    // /// <summary>
    // /// 预加载并实例化对象到预加载组
    // /// </summary>
    // private async UniTask PreloadAndInstantiateObjects(PreloadGroup group, NearbyGridInfo nearbyInfo)
    // {
    //     var loadTasks = new List<UniTask>();

    //     // 处理OtherUnit
    //     foreach (var kvp in nearbyInfo.otherUnits)
    //     {
    //         foreach (var otherUnit in kvp.Value)
    //         {
    //             if (!string.IsNullOrEmpty(otherUnit.prefabAddress))
    //             {
    //                 loadTasks.Add(PreloadAndInstantiateObject(group, otherUnit.prefabAddress,
    //                     otherUnit.position, true, otherUnit, null));
    //             }
    //         }
    //     }

    //     // 处理MapBlock
    //     foreach (var kvp in nearbyInfo.mapBlocks)
    //     {
    //         var mapBlock = kvp.Value;
    //         if (!string.IsNullOrEmpty(mapBlock.prefabAddress))
    //         {
    //             loadTasks.Add(PreloadAndInstantiateObject(group, mapBlock.prefabAddress,
    //                 mapBlock.localPosition, false, null, mapBlock));
    //         }
    //     }

    //     // 并发执行，但限制数量
    //     if (loadTasks.Count > 0)
    //     {
    //         await UniTask.WhenAll(loadTasks);
    //     }
    // }

    // /// <summary>
    // /// 预加载并实例化单个对象
    // /// </summary>
    // private async UniTask PreloadAndInstantiateObject(PreloadGroup group, string prefabAddress,
    //     Vector3 position, bool isOtherUnit, MapOtherUnitInfoPack otherUnit, MapBlock mapBlock)
    // {
    //     try
    //     {
    //         GameObject prefab = null;

    //         // 先检查组内缓存
    //         if (!group.preloadedPrefabs.TryGetValue(prefabAddress, out prefab))
    //         {
    //             // 使用统一的缓存获取方法
    //             prefab = await GetOrLoadPrefab(prefabAddress);

    //             if (prefab != null)
    //             {
    //                 group.preloadedPrefabs[prefabAddress] = prefab;
    //             }
    //         }

    //         if (prefab != null)
    //         {
    //             // 实例化对象
    //             var objs = await GameObject.InstantiateAsync(prefab, _mapContainer).ToUniTask();
    //             if (objs != null && objs.Length > 0)
    //             {
    //                 GameObject instance = objs[0];
    //                 instance.transform.SetParent(_mapContainer);
    //                 instance.transform.localPosition = position;

    //                 if (isOtherUnit && otherUnit != null)
    //                 {
    //                     instance.transform.localRotation = Quaternion.Euler(otherUnit.rotation);
    //                     instance.transform.localScale = otherUnit.scale;
    //                     instance.name = otherUnit.name;
    //                 }
    //                 else if (mapBlock != null)
    //                 {
    //                     instance.name = mapBlock.blockName;
    //                 }

    //                 // 添加到格子管理系统
    //                 Vector2Int gridCoord = WorldToGridCoord(_mapContainer.InverseTransformPoint(position));
    //                 AddObjectToGrid(instance, gridCoord, GridType.Preload, group.groupId);

    //                 // 记录到预加载组
    //                 if (!group.preloadedGrids.Contains(gridCoord))
    //                 {
    //                     group.preloadedGrids.Add(gridCoord);
    //                 }

    //                 if (debugLog)
    //                 {
    //                     Debug.Log($"预加载对象: {prefabAddress} for group {group.groupId} at grid {gridCoord}");
    //                 }
    //             }
    //         }
    //     }
    //     catch (System.Exception e)
    //     {
    //         Debug.LogError($"预加载对象失败 {prefabAddress}: {e.Message}");
    //     }
    // }

    /// <summary>
    /// 加载任务类
    /// </summary>
    private class LoadTask
    {
        public Vector2Int gridCoord;
        public string prefabAddress;
        public Vector3 position;
        public bool isOtherUnit;
        public bool isPreload;
        public MapOtherUnitInfoPack otherUnitInfo;
        public MapBlock mapBlockInfo;
    }

    /// <summary>
    /// 预加载组类 - 管理传送点相关的预加载资源
    /// </summary>
    // private class PreloadGroup
    // {
    //     public string groupId;
    //     public Vector3 centerPosition;
    //     public Dictionary<string, GameObject> preloadedPrefabs = new();
    //     public List<Vector2Int> preloadedGrids = new(); // 改为记录格子坐标
    //     public bool isActive = true;
    //     public float lastAccessTime;

    //     public PreloadGroup(string id, Vector3 center)
    //     {
    //         groupId = id;
    //         centerPosition = center;
    //         lastAccessTime = Time.time;
    //     }
    // }

    /// <summary>
    /// 附近格子信息的结果类
    /// </summary>
    public class NearbyGridInfo
    {
        public Dictionary<Vector2Int, List<MapOtherUnitInfoPack>> otherUnits = new Dictionary<Vector2Int, List<MapOtherUnitInfoPack>>();
        public Dictionary<Vector2Int, MapBlock> mapBlocks = new Dictionary<Vector2Int, MapBlock>();
    }
}