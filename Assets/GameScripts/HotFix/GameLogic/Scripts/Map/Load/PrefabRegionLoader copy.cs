using System.Collections.Generic;
using UnityEngine;
using System.IO;
using Cysharp.Threading.Tasks;
using System.Threading;

/// <summary>
/// 区域预制体加载器，支持三维空间哈希，动态加载/卸载场景中的物体（包括传送点）
/// </summary>
public class PrefabRegionLoader
{

    // 所有预制体数据的原始坐标映射（Vector3 -> 数据）
    // private Dictionary<Vector3, PrefabData> _prefabMap = new();
    private Dictionary<Vector2Int, List<MapOtherUnitInfoPack>> _otherUnitInfoPackMap = new();
    private Dictionary<Vector2Int, MapBlock> _mainMapBlocks = new();

    // 三维空间哈希字典，key 为空间格子（cell），value 是该格子内的 prefab 列表
    // private Dictionary<Vector3Int, List<PrefabItem>> _spatialHashMap = new();

    // 已加载的位置集合
    // private HashSet<Vector3> _loadedPositions = new();

    // 当前加载在场景中的实例列表
    private List<LoadedItem> _loadedInstances = new();

    // 缓存的附近格子中的所有 prefab 列表（避免重复查找）
    // private List<PrefabItem> _cachedNearbyPrefabs = new();

    // 地图挂载容器（通常是空 GameObject）
    private Transform _mapContainer;

    // 加载半径，单位是世界坐标（米）
    // public float loadRadius = 70f;

    // 是否打印调试信息
    public bool debugLog = false;

    // 每个空间格子的大小（立方体边长），单位：米
    private readonly int _cellSize = 32;

    // 卸载半径，设为加载半径的 1.5 倍
    // private float unloadRadius => loadRadius * 1.5f;

    // 所有传送点信息（根据 prefab 数据附带的结构）
    private Dictionary<string, MapTransmitPointInfoPack> _transmitPoints = new();

    // 当前场景中已生成的传送点对象（按名称索引）
    private Dictionary<string, GameObject> _loadedTransmitPoints = new();

    // 预制体缓存系统
    private Dictionary<string, GameObject> _prefabCache = new();

    // 正式加载的对象管理
    private Dictionary<Vector2Int, List<GameObject>> _loadedGridObjects = new();
    private HashSet<Vector2Int> _loadingGrids = new(); //加载中的
    private Queue<LoadTask> _loadQueue = new();

    // 预加载系统（独立管理）
    private Dictionary<string, PreloadGroup> _preloadGroups = new(); // 按传送点分组的预加载
    private Dictionary<string, GameObject> _globalPreloadCache = new(); // 全局预加载缓存
    private HashSet<string> _preloadingPrefabs = new();

    // 是否正在异步加载中
    private bool _isloading = false;
    private IMapInfo _mapInfo;
    private CancellationTokenSource _loadCancellationTokenSource;

    // 加载任务相关
    private bool _isProcessingQueue = false;
    private readonly int _maxConcurrentLoads = 3; // 最大并发加载数
    private int _currentLoadingCount = 0;

    // 回调事件
    public System.Action<Vector3, bool> OnLoadCompleted; // 位置, 是否成功
    public System.Action<string, bool> OnPreloadCompleted; // 预加载组名, 是否成功
    /// <summary>
    /// 构造函数，初始化区域加载器
    /// </summary>
    public PrefabRegionLoader(Transform mapContainer, IMapInfo mapInfo)
    {
        _mapInfo = mapInfo;
        // regionName = "HeartGold"; // 示例强制赋值，可改为参数传入
        // _regionName = regionName;
        _mapContainer = mapContainer;
        // _configFileNames = configFileNames;
        InitData();
    }

    /// <summary>
    /// 加载给定位置附近的对象（支持动态卸载远处对象）
    /// </summary>
    public void LoadNearby(Vector3 worldPosition)
    {
        AsyncLoadNearby(worldPosition, 5).Forget();
    }

    /// <summary>
    /// 异步加载附近 prefab 的主逻辑函数
    /// </summary>
    public async UniTask AsyncLoadNearby(Vector3 worldPosition, int frame = 0)
    {
        if (_isloading) return;
        if (_mapContainer == null)
        {
            Debug.LogError("Map container is not set.");
            return;
        }

        _isloading = true;
        _loadCancellationTokenSource = new CancellationTokenSource();
        var token = _loadCancellationTokenSource.Token;

        try
        {
            // 转换为 local 坐标
            Vector3 localPosition = _mapContainer.InverseTransformPoint(worldPosition);

            // 获取附近格子信息
            var nearbyInfo = GetNearbyGridInfo(worldPosition, Mathf.CeilToInt(loadRadius / _cellSize));

            // 创建加载任务
            await CreateLoadTasks(nearbyInfo, token);

            // 开始处理加载队列
            if (!_isProcessingQueue)
            {
                ProcessLoadQueue(token).Forget();
            }

            // 卸载远处的对象
            UnloadFar(localPosition);

            if (debugLog)
            {
                Debug.Log($"AsyncLoadNearby completed at position: {worldPosition}");
            }

            // 触发加载完成回调
            OnLoadCompleted?.Invoke(worldPosition, true);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in AsyncLoadNearby: {e.Message}");
            OnLoadCompleted?.Invoke(worldPosition, false);
        }
        finally
        {
            _isloading = false;
        }
    }
    public void CancelLoading()
    {
        if (_isloading)
        {
            _loadCancellationTokenSource?.Cancel();
            _loadCancellationTokenSource?.Dispose();
            _isloading = false;
        }
    }
    /// <summary>
    /// 卸载离当前位置太远的正式加载对象（不包括预加载对象）
    /// </summary>
    private void UnloadFar(Vector3 localPos)
    {
        for (int i = _loadedInstances.Count - 1; i >= 0; i--)
        {
            var item = _loadedInstances[i];

            // 跳过预加载对象，它们由预加载组管理
            if (item.isPreloaded)
                continue;

            float distance = Vector3.Distance(localPos, item.position);
            if (distance > unloadRadius)
            {
                UnloadSingleObject(item, i);
            }
        }
    }

    /// <summary>
    /// 卸载单个对象
    /// </summary>
    private void UnloadSingleObject(LoadedItem item, int index = -1)
    {
        // 检查是否是传送点
        if (_loadedTransmitPoints.ContainsValue(item.instance))
        {
            var transmitComponent = item.instance.GetComponent<TransmitMapComponent>();
            if (transmitComponent != null)
            {
                _loadedTransmitPoints.Remove(transmitComponent.Name);
            }
        }

        // 从相关字典中移除
        _loadedGridObjects.Remove(item.gridCoord);
        // _loadedPositions.Remove(item.position);

        // 销毁实例
        if (item.instance != null)
        {
            GameObject.Destroy(item.instance);
        }

        // 从列表中移除
        if (index >= 0)
        {
            _loadedInstances.RemoveAt(index);
        }
        else
        {
            _loadedInstances.Remove(item);
        }

        if (debugLog)
        {
            string type = item.isPreloaded ? "预加载" : "正式加载";
            Debug.Log($"♻️ 卸载{type}对象: {item.prefabAddress} at {item.position}, grid: {item.gridCoord}");
        }
    }

    /// <summary>
    /// 清理预制体缓存
    /// </summary>
    public void ClearPrefabCache()
    {
        _prefabCache.Clear();
        _globalPreloadCache.Clear();

        if (debugLog)
            Debug.Log("Prefab cache cleared");
    }

    /// <summary>
    /// 清理指定预加载组
    /// </summary>
    public void ClearPreloadGroup(string groupId)
    {
        if (_preloadGroups.TryGetValue(groupId, out var group))
        {
            // 卸载该组的所有预加载对象
            foreach (var item in group.preloadedObjects)
            {
                UnloadSingleObject(item);
            }

            // 清理预制体缓存
            group.preloadedPrefabs.Clear();
            group.preloadedObjects.Clear();

            _preloadGroups.Remove(groupId);

            if (debugLog)
                Debug.Log($"Cleared preload group: {groupId}");
        }
    }

    /// <summary>
    /// 清理所有预加载组
    /// </summary>
    public void ClearAllPreloadGroups()
    {
        var groupIds = new List<string>(_preloadGroups.Keys);
        foreach (var groupId in groupIds)
        {
            ClearPreloadGroup(groupId);
        }
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    public void LogCacheStats()
    {
        Debug.Log($"Cache Stats - Prefabs: {_prefabCache.Count}, Loaded Objects: {_loadedGridObjects.Count}, " +
                  $"Loading Grids: {_loadingGrids.Count}, Queue: {_loadQueue.Count}");
    }

    /// <summary>
    /// 智能加载：先预加载，再正常加载
    /// </summary>
    public async UniTask SmartLoadNearby(Vector3 worldPosition, int preloadDepth = 3)
    {
        // 先预加载更大范围的预制体
        await PreloadNearby(worldPosition, preloadDepth);

        // 再正常加载当前范围的对象
        await AsyncLoadNearby(worldPosition);

        if (debugLog)
        {
            Debug.Log($"Smart load completed for position: {worldPosition}");
        }
    }

    /// <summary>
    /// 检查指定格子是否已加载
    /// </summary>
    public bool IsGridLoaded(Vector2Int gridCoord)
    {
        return _loadedGridObjects.ContainsKey(gridCoord);
    }

    /// <summary>
    /// 获取指定格子的已加载对象
    /// </summary>
    public List<GameObject> GetLoadedGridObject(Vector2Int gridCoord)
    {
        _loadedGridObjects.TryGetValue(gridCoord, out var obj);
        return obj;
    }

    /// <summary>
    /// 检查预加载组是否存在
    /// </summary>
    public bool HasPreloadGroup(string groupId)
    {
        return _preloadGroups.ContainsKey(groupId);
    }

    /// <summary>
    /// 获取预加载组信息
    /// </summary>
    public (int objectCount, Vector3 centerPosition) GetPreloadGroupInfo(string groupId)
    {
        if (_preloadGroups.TryGetValue(groupId, out var group))
        {
            return (group.preloadedObjects.Count, group.centerPosition);
        }
        return (0, Vector3.zero);
    }

    /// <summary>
    /// 激活/停用预加载组（显示/隐藏对象）
    /// </summary>
    public void SetPreloadGroupActive(string groupId, bool active)
    {
        if (_preloadGroups.TryGetValue(groupId, out var group))
        {
            group.isActive = active;
            foreach (var item in group.preloadedObjects)
            {
                if (item.instance != null)
                {
                    item.instance.SetActive(active);
                }
            }

            if (debugLog)
            {
                Debug.Log($"预加载组 {groupId} 设置为 {(active ? "激活" : "停用")}");
            }
        }
    }

    /// <summary>
    /// 初始化数据：解析 JSON 配置、构建哈希表
    /// </summary>
    private void InitData()
    {
        _otherUnitInfoPackMap.Clear();
        _mainMapBlocks.Clear();
        var mapData = _mapInfo.GetMapData();
        if(mapData == null) {
            return;
        }
        if(mapData.otherUnits != null) {
            _otherUnitInfoPackMap = mapData.otherUnits;
        }
        if(mapData.mainBlocks != null) {
            _mainMapBlocks = mapData.mainBlocks;
        }
    }

    /// <summary>
    /// 将世界坐标转为二维格子坐标（只考虑x和z）
    /// </summary>
    private Vector2Int WorldToGridCoord(Vector3 worldPos)
    {
        return new Vector2Int(
            Mathf.RoundToInt(worldPos.x / _cellSize),
            Mathf.RoundToInt(worldPos.z / _cellSize)
        );
    }

    /// <summary>
    /// 根据世界坐标查找附近格子的信息
    /// </summary>
    /// <param name="worldPosition">世界坐标</param>
    /// <param name="searchDepth">搜索深度（格子距离），默认为2</param>
    /// <returns>包含附近格子信息的结果</returns>
    public NearbyGridInfo GetNearbyGridInfo(Vector3 worldPosition, int searchDepth = 2)
    {
        // 转换为本地坐标
        Vector3 localPosition = _mapContainer.InverseTransformPoint(worldPosition);

        // 转换为格子坐标
        Vector2Int centerGrid = WorldToGridCoord(localPosition);

        var result = new NearbyGridInfo();

        // 搜索周围的格子
        for (int x = centerGrid.x - searchDepth; x <= centerGrid.x + searchDepth; x++)
        {
            for (int z = centerGrid.y - searchDepth; z <= centerGrid.y + searchDepth; z++)
            {
                Vector2Int gridCoord = new Vector2Int(x, z);

                // 查找OtherUnit信息
                if (_otherUnitInfoPackMap.TryGetValue(gridCoord, out var otherUnit))
                {
                    result.otherUnits.Add(gridCoord, otherUnit);
                }

                // 查找MapBlock信息
                if (_mainMapBlocks.TryGetValue(gridCoord, out var mapBlock))
                {
                    result.mapBlocks.Add(gridCoord, mapBlock);
                }
            }
        }

        if (debugLog)
        {
            Debug.Log($"在坐标 {worldPosition} 附近找到 {result.otherUnits.Count} 个OtherUnit和 {result.mapBlocks.Count} 个MapBlock");
        }

        return result;
    }

    /// <summary>
    /// 创建加载任务
    /// </summary>
    private async UniTask CreateLoadTasks(NearbyGridInfo nearbyInfo, CancellationToken token)
    {
        // 处理OtherUnit
        foreach (var kvp in nearbyInfo.otherUnits)
        {
            var gridCoord = kvp.Key;
            var otherUnit = kvp.Value;

            if (_loadedGridObjects.ContainsKey(gridCoord) || _loadingGrids.Contains(gridCoord))
                continue;
            foreach (var unit in otherUnit)
            {
                if (!string.IsNullOrEmpty(unit.prefabAddress))
                {
                    var task = new LoadTask
                    {
                        gridCoord = gridCoord,
                        prefabAddress = unit.prefabAddress,
                        position = unit.position,
                        isOtherUnit = true,
                        otherUnitInfo = unit
                    };
                    _loadQueue.Enqueue(task);
                    _loadingGrids.Add(gridCoord);
                }
            }
        }

        // 处理MapBlock
        foreach (var kvp in nearbyInfo.mapBlocks)
        {
            var gridCoord = kvp.Key;
            var mapBlock = kvp.Value;

            if (_loadedGridObjects.ContainsKey(gridCoord) || _loadingGrids.Contains(gridCoord))
                continue;

            if (!string.IsNullOrEmpty(mapBlock.prefabAddress))
            {
                var task = new LoadTask
                {
                    gridCoord = gridCoord,
                    prefabAddress = mapBlock.prefabAddress,
                    position = mapBlock.localPosition,
                    isOtherUnit = false,
                    mapBlockInfo = mapBlock
                };

                _loadQueue.Enqueue(task);
                _loadingGrids.Add(gridCoord);
            }
        }

        await UniTask.Yield(PlayerLoopTiming.Update, token);
    }

    /// <summary>
    /// 处理加载队列
    /// </summary>
    private async UniTask ProcessLoadQueue(CancellationToken token)
    {
        _isProcessingQueue = true;

        try
        {
            while (_loadQueue.Count > 0 && !token.IsCancellationRequested)
            {
                // 控制并发加载数量
                if (_currentLoadingCount >= _maxConcurrentLoads)
                {
                    await UniTask.Delay(50, cancellationToken: token);
                    continue;
                }

                var task = _loadQueue.Dequeue();
                LoadPrefabAsync(task, token).Forget();

                // 分帧处理，避免卡顿
                await UniTask.Yield(PlayerLoopTiming.Update, token);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in ProcessLoadQueue: {e.Message}");
        }
        finally
        {
            _isProcessingQueue = false;
        }
    }

    /// <summary>
    /// 异步加载单个预制体
    /// </summary>
    private async UniTask LoadPrefabAsync(LoadTask task, CancellationToken token)
    {
        _currentLoadingCount++;

        try
        {
            GameObject prefab = null;

            // 先检查缓存
            if (_prefabCache.TryGetValue(task.prefabAddress, out prefab))
            {
                await InstantiatePrefab(prefab, task, token);
            }
            else
            {
                // 异步加载预制体
                prefab = await AssertResourceLoader.Share.LoadAssetAsync<GameObject>(task.prefabAddress);
                if (prefab != null && !token.IsCancellationRequested)
                {
                    // 缓存预制体
                    _prefabCache[task.prefabAddress] = prefab;
                    await InstantiatePrefab(prefab, task, token);
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error loading prefab {task.prefabAddress}: {e.Message}");
        }
        finally
        {
            _currentLoadingCount--;
            _loadingGrids.Remove(task.gridCoord);
        }
    }

    /// <summary>
    /// 实例化预制体
    /// </summary>
    private async UniTask InstantiatePrefab(GameObject prefab, LoadTask task, CancellationToken token)
    {
        if (token.IsCancellationRequested) return;

        var objs = await GameObject.InstantiateAsync(prefab, _mapContainer).ToUniTask();
        if (objs == null || objs.Length == 0) return;

        GameObject instance = objs[0];
        instance.transform.SetParent(_mapContainer);
        instance.transform.localPosition = task.position;

        if (task.isOtherUnit)
        {
            // 处理OtherUnit
            instance.transform.localRotation = Quaternion.Euler(task.otherUnitInfo.rotation);
            instance.transform.localScale = task.otherUnitInfo.scale;
            instance.name = task.otherUnitInfo.name;
        }
        else
        {
            // 处理MapBlock
            instance.name = task.mapBlockInfo.blockName;
        }

        // 记录已加载的对象
        var loadedItem = new LoadedItem
        {
            instance = instance,
            position = task.position,
            gridCoord = task.gridCoord,
            prefabAddress = task.prefabAddress
        };

        _loadedInstances.Add(loadedItem);
        if(!_loadedGridObjects.ContainsKey(task.gridCoord))
        {
            _loadedGridObjects[task.gridCoord] = new List<GameObject>();
        }
        _loadedGridObjects[task.gridCoord].Add(instance);

        if (debugLog)
        {
            Debug.Log($"✅ Loaded: {task.prefabAddress} at grid {task.gridCoord}");
        }
    }

    /// <summary>
    /// 预加载指定位置附近的预制体
    /// </summary>
    public async UniTask PreloadNearby(Vector3 worldPosition, int preloadDepth = 3)
    {
        var nearbyInfo = GetNearbyGridInfo(worldPosition, preloadDepth);
        var prefabsToPreload = new HashSet<string>();

        // 收集需要预加载的预制体地址
        foreach (var otherUnit in nearbyInfo.otherUnits.Values)
        {
            foreach (var unit in otherUnit)
            {
                if (!string.IsNullOrEmpty(unit.prefabAddress))
                    prefabsToPreload.Add(unit.prefabAddress);
            }
        }

        foreach (var mapBlock in nearbyInfo.mapBlocks.Values)
        {
            if (!string.IsNullOrEmpty(mapBlock.prefabAddress))
                prefabsToPreload.Add(mapBlock.prefabAddress);
        }

        // 异步预加载
        var preloadTasks = new List<UniTask>();
        foreach (var prefabAddress in prefabsToPreload)
        {
            if (!_prefabCache.ContainsKey(prefabAddress) && !_preloadingPrefabs.Contains(prefabAddress))
            {
                preloadTasks.Add(PreloadSinglePrefab(prefabAddress));
            }
        }

        if (preloadTasks.Count > 0)
        {
            await UniTask.WhenAll(preloadTasks);
        }

        if (debugLog)
        {
            Debug.Log($"Preloaded {preloadTasks.Count} prefabs for position {worldPosition}");
        }
    }

    /// <summary>
    /// 预加载单个预制体
    /// </summary>
    private async UniTask PreloadSinglePrefab(string prefabAddress)
    {
        if (_preloadingPrefabs.Contains(prefabAddress)) return;

        _preloadingPrefabs.Add(prefabAddress);

        try
        {
            var prefab = await AssertResourceLoader.Share.LoadAssetAsync<GameObject>(prefabAddress);
            if (prefab != null)
            {
                _prefabCache[prefabAddress] = prefab;
                _globalPreloadCache[prefabAddress] = prefab;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error preloading prefab {prefabAddress}: {e.Message}");
        }
        finally
        {
            _preloadingPrefabs.Remove(prefabAddress);
        }
    }

    public MapTransmitPointInfoPack GetTransmitPointInfo(string transmitPointName)
    {
        _transmitPoints.TryGetValue(transmitPointName, out var info);
        return info;
    }

    public GameObject GetLoadedTransmitPoint(string transmitPointName)
    {
        _loadedTransmitPoints.TryGetValue(transmitPointName, out var obj);
        return obj;
    }

    /// <summary>
    /// 为传送点预加载指定位置的对象（实际实例化到场景中）
    /// </summary>
    /// <param name="groupId">预加载组ID（通常是传送点名称）</param>
    /// <param name="targetPosition">目标位置</param>
    /// <param name="preloadDepth">预加载深度</param>
    public async UniTask PreloadForTeleport(string groupId, Vector3 targetPosition, int preloadDepth = 2)
    {
        try
        {
            // 如果组已存在，先清理
            if (_preloadGroups.ContainsKey(groupId))
            {
                ClearPreloadGroup(groupId);
            }

            // 创建新的预加载组
            var group = new PreloadGroup(groupId, targetPosition);
            _preloadGroups[groupId] = group;

            // 获取目标位置附近的格子信息
            var nearbyInfo = GetNearbyGridInfo(targetPosition, preloadDepth);

            // 预加载并实例化对象
            await PreloadAndInstantiateObjects(group, nearbyInfo);

            // 触发回调
            OnPreloadCompleted?.Invoke(groupId, true);

            if (debugLog)
            {
                Debug.Log($"✅ 传送点预加载完成: {groupId}, 对象数量: {group.preloadedObjects.Count}");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"传送点预加载失败 {groupId}: {e.Message}");
            OnPreloadCompleted?.Invoke(groupId, false);
        }
    }

    /// <summary>
    /// 预加载并实例化对象到预加载组
    /// </summary>
    private async UniTask PreloadAndInstantiateObjects(PreloadGroup group, NearbyGridInfo nearbyInfo)
    {
        var loadTasks = new List<UniTask>();

        // 处理OtherUnit
        foreach (var kvp in nearbyInfo.otherUnits)
        {
            foreach (var otherUnit in kvp.Value)
            {
                if (!string.IsNullOrEmpty(otherUnit.prefabAddress))
                {
                    loadTasks.Add(PreloadAndInstantiateObject(group, otherUnit.prefabAddress,
                        otherUnit.position, true, otherUnit, null));
                }
            }
        }

        // 处理MapBlock
        foreach (var kvp in nearbyInfo.mapBlocks)
        {
            var mapBlock = kvp.Value;
            if (!string.IsNullOrEmpty(mapBlock.prefabAddress))
            {
                loadTasks.Add(PreloadAndInstantiateObject(group, mapBlock.prefabAddress,
                    mapBlock.localPosition, false, null, mapBlock));
            }
        }

        // 并发执行，但限制数量
        if (loadTasks.Count > 0)
        {
            await UniTask.WhenAll(loadTasks);
        }
    }

    /// <summary>
    /// 预加载并实例化单个对象
    /// </summary>
    private async UniTask PreloadAndInstantiateObject(PreloadGroup group, string prefabAddress,
        Vector3 position, bool isOtherUnit, MapOtherUnitInfoPack otherUnit, MapBlock mapBlock)
    {
        try
        {
            GameObject prefab = null;

            // 先检查组内缓存
            if (!group.preloadedPrefabs.TryGetValue(prefabAddress, out prefab))
            {
                // 检查全局缓存
                if (!_prefabCache.TryGetValue(prefabAddress, out prefab))
                {
                    // 异步加载
                    prefab = await AssertResourceLoader.Share.LoadAssetAsync<GameObject>(prefabAddress);
                    if (prefab != null)
                    {
                        _prefabCache[prefabAddress] = prefab;
                    }
                }

                if (prefab != null)
                {
                    group.preloadedPrefabs[prefabAddress] = prefab;
                }
            }

            if (prefab != null)
            {
                // 实例化对象
                var objs = await GameObject.InstantiateAsync(prefab, _mapContainer).ToUniTask();
                if (objs != null && objs.Length > 0)
                {
                    GameObject instance = objs[0];
                    instance.transform.SetParent(_mapContainer);
                    instance.transform.localPosition = position;

                    if (isOtherUnit && otherUnit != null)
                    {
                        instance.transform.localRotation = Quaternion.Euler(otherUnit.rotation);
                        instance.transform.localScale = otherUnit.scale;
                        instance.name = otherUnit.name;
                    }
                    else if (mapBlock != null)
                    {
                        instance.name = mapBlock.blockName;
                    }

                    // 创建预加载项
                    var loadedItem = new LoadedItem
                    {
                        instance = instance,
                        position = position,
                        gridCoord = WorldToGridCoord(_mapContainer.InverseTransformPoint(position)),
                        prefabAddress = prefabAddress,
                        isPreloaded = true,
                        preloadGroupId = group.groupId
                    };

                    group.preloadedObjects.Add(loadedItem);
                    _loadedInstances.Add(loadedItem);

                    if (debugLog)
                    {
                        Debug.Log($"预加载对象: {prefabAddress} for group {group.groupId}");
                    }
                }
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"预加载对象失败 {prefabAddress}: {e.Message}");
        }
    }

    private class LoadedItem
    {
        public GameObject instance;
        public Vector3 position;
        public Vector2Int gridCoord;
        public string prefabAddress;
        public bool isPreloaded; // 是否为预加载对象
        public string preloadGroupId; // 预加载组ID
    }

    /// <summary>
    /// 加载任务类
    /// </summary>
    private class LoadTask
    {
        public Vector2Int gridCoord;
        public string prefabAddress;
        public Vector3 position;
        public bool isOtherUnit;
        public MapOtherUnitInfoPack otherUnitInfo;
        public MapBlock mapBlockInfo;
    }

    /// <summary>
    /// 预加载组类 - 管理传送点相关的预加载资源
    /// </summary>
    private class PreloadGroup
    {
        public string groupId;
        public Vector3 centerPosition;
        public Dictionary<string, GameObject> preloadedPrefabs = new();
        public List<LoadedItem> preloadedObjects = new();
        public bool isActive = true;
        public float lastAccessTime;

        public PreloadGroup(string id, Vector3 center)
        {
            groupId = id;
            centerPosition = center;
            lastAccessTime = Time.time;
        }
    }

    /// <summary>
    /// 附近格子信息的结果类
    /// </summary>
    public class NearbyGridInfo
    {
        public Dictionary<Vector2Int, List<MapOtherUnitInfoPack>> otherUnits = new Dictionary<Vector2Int, List<MapOtherUnitInfoPack>>();
        public Dictionary<Vector2Int, MapBlock> mapBlocks = new Dictionary<Vector2Int, MapBlock>();
    }
}