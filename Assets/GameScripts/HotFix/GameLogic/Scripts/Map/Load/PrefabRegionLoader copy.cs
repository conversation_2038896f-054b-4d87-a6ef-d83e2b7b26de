using System.Collections.Generic;
using UnityEngine;
using System.IO;
using Cysharp.Threading.Tasks;
using System.Threading;

/// <summary>
/// 区域预制体加载器，支持三维空间哈希，动态加载/卸载场景中的物体（包括传送点）
/// </summary>
public class PrefabRegionLoader
{

    // 所有预制体数据的原始坐标映射（Vector3 -> 数据）
    // private Dictionary<Vector3, PrefabData> _prefabMap = new();
    private Dictionary<Vector2Int, MapOtherUnitInfoPack> _otherUnitInfoPackMap = new();
    private Dictionary<Vector2Int, MapBlock> _mainMapBlocks = new();

    // 三维空间哈希字典，key 为空间格子（cell），value 是该格子内的 prefab 列表
    // private Dictionary<Vector3Int, List<PrefabItem>> _spatialHashMap = new();

    // 已加载的位置集合
    private HashSet<Vector3> _loadedPositions = new();

    // 当前加载在场景中的实例列表
    private List<LoadedItem> _loadedInstances = new();

    // 缓存的附近格子中的所有 prefab 列表（避免重复查找）
    // private List<PrefabItem> _cachedNearbyPrefabs = new();

    // 地图挂载容器（通常是空 GameObject）
    private Transform _mapContainer;

    private string _regionName;
    private Vector3? _lastCenter = null;

    // 加载半径，单位是世界坐标（米）
    public float loadRadius = 70f;

    // 是否打印调试信息
    public bool debugLog = false;

    // 每个空间格子的大小（立方体边长），单位：米
    private readonly int _cellSize = 32;

    // 卸载半径，设为加载半径的 1.5 倍
    private float unloadRadius => loadRadius * 1.5f;

    // 所有传送点信息（根据 prefab 数据附带的结构）
    private Dictionary<string, MapTransmitPointInfoPack> _transmitPoints = new();

    // 当前场景中已生成的传送点对象（按名称索引）
    private Dictionary<string, GameObject> _loadedTransmitPoints = new();

    // 是否正在异步加载中
    private bool _isloading = false;
    private IMapInfo _mapInfo;
    private CancellationTokenSource _loadCancellationTokenSource;
    /// <summary>
    /// 构造函数，初始化区域加载器
    /// </summary>
    public PrefabRegionLoader(Transform mapContainer, IMapInfo mapInfo)
    {
        _mapInfo = mapInfo;
        // regionName = "HeartGold"; // 示例强制赋值，可改为参数传入
        // _regionName = regionName;
        _mapContainer = mapContainer;
        // _configFileNames = configFileNames;
        InitData();
    }

    /// <summary>
    /// 加载给定位置附近的对象（支持动态卸载远处对象）
    /// </summary>
    public void LoadNearby(Vector3 worldPosition)
    {
        AsyncLoadNearby(worldPosition, 5).Forget();
    }

    /// <summary>
    /// 异步加载附近 prefab 的主逻辑函数
    /// </summary>
    public async UniTask AsyncLoadNearby(Vector3 worldPosition, int frame = 0)
    {
        if (_isloading) return;
        if (_mapContainer == null)
        {
            Debug.LogError("Map container is not set.");
            return;
        }

        _isloading = true;
        _loadCancellationTokenSource = new CancellationTokenSource();
        var token = _loadCancellationTokenSource.Token;

        // 转换为 local 坐标
        Vector3 localPosition = _mapContainer.InverseTransformPoint(worldPosition);

        // 注意：原有的空间哈希功能已被注释，这里暂时跳过缓存逻辑
        // 如果需要恢复原有的预制体加载功能，需要重新实现_spatialHashMap相关代码
        _lastCenter = localPosition;
        
        // 原有的预制体加载逻辑已被移除，因为依赖的_spatialHashMap和PrefabItem已被注释
        // var cachedNearbyCopy = new List<PrefabItem>(_cachedNearbyPrefabs);

        // 原有的预制体加载逻辑已被注释，因为依赖的_spatialHashMap和PrefabItem已被注释
        // 如果需要恢复预制体加载功能，需要重新实现相关的数据结构和逻辑

        if (debugLog)
        {
            Debug.Log($"AsyncLoadNearby called at position: {worldPosition}, but prefab loading is currently disabled");
        }

        UnloadFar(localPosition);
        _isloading = false;
        return;
    }
    public void CancelLoading()
    {
        if (_isloading)
        {
            _loadCancellationTokenSource?.Cancel();
            _loadCancellationTokenSource?.Dispose();
            _isloading = false;
        }
    }
    /// <summary>
    /// 卸载离当前位置太远的 prefab 实例
    /// </summary>
    private void UnloadFar(Vector3 localPos)
    {
        for (int i = _loadedInstances.Count - 1; i >= 0; i--)
        {
            var item = _loadedInstances[i];
            if (Vector3.Distance(localPos, item.position) > unloadRadius)
            {
                if (_loadedTransmitPoints.ContainsValue(item.instance))
                {
                    var transmitComponent = item.instance.GetComponent<TransmitMapComponent>();
                    if (transmitComponent != null)
                    {
                        _loadedTransmitPoints.Remove(transmitComponent.Name);
                    }
                }

                GameObject.Destroy(item.instance);
                _loadedPositions.Remove(item.position);
                _loadedInstances.RemoveAt(i);

                if (debugLog)
                    Debug.Log($"♻️ Unloaded object at {item.position}");
            }
        }
    }

    /// <summary>
    /// 初始化数据：解析 JSON 配置、构建哈希表
    /// </summary>
    private void InitData()
    {
        _otherUnitInfoPackMap.Clear();
        _mainMapBlocks.Clear();
        var mapData = _mapInfo.GetMapData();
        if(mapData == null) {
            return;
        }
        if(mapData.otherUnits != null) {
            _otherUnitInfoPackMap = mapData.otherUnits;
        }
        if(mapData.mainBlocks != null) {
            _mainMapBlocks = mapData.mainBlocks;
        }
    }

    /// <summary>
    /// 将世界坐标转为二维格子坐标（只考虑x和z）
    /// </summary>
    private Vector2Int WorldToGridCoord(Vector3 worldPos)
    {
        return new Vector2Int(
            Mathf.RoundToInt(worldPos.x / _cellSize),
            Mathf.RoundToInt(worldPos.z / _cellSize)
        );
    }

    /// <summary>
    /// 根据世界坐标查找附近格子的信息
    /// </summary>
    /// <param name="worldPosition">世界坐标</param>
    /// <param name="searchDepth">搜索深度（格子距离），默认为2</param>
    /// <returns>包含附近格子信息的结果</returns>
    public NearbyGridInfo GetNearbyGridInfo(Vector3 worldPosition, int searchDepth = 2)
    {
        // 转换为本地坐标
        Vector3 localPosition = _mapContainer.InverseTransformPoint(worldPosition);

        // 转换为格子坐标
        Vector2Int centerGrid = WorldToGridCoord(localPosition);

        var result = new NearbyGridInfo();

        // 搜索周围的格子
        for (int x = centerGrid.x - searchDepth; x <= centerGrid.x + searchDepth; x++)
        {
            for (int z = centerGrid.y - searchDepth; z <= centerGrid.y + searchDepth; z++)
            {
                Vector2Int gridCoord = new Vector2Int(x, z);

                // 查找OtherUnit信息
                if (_otherUnitInfoPackMap.TryGetValue(gridCoord, out var otherUnit))
                {
                    result.otherUnits.Add(gridCoord, otherUnit);
                }

                // 查找MapBlock信息
                if (_mainMapBlocks.TryGetValue(gridCoord, out var mapBlock))
                {
                    result.mapBlocks.Add(gridCoord, mapBlock);
                }
            }
        }

        if (debugLog)
        {
            Debug.Log($"在坐标 {worldPosition} 附近找到 {result.otherUnits.Count} 个OtherUnit和 {result.mapBlocks.Count} 个MapBlock");
        }

        return result;
    }

    public MapTransmitPointInfoPack GetTransmitPointInfo(string transmitPointName)
    {
        _transmitPoints.TryGetValue(transmitPointName, out var info);
        return info;
    }

    public GameObject GetLoadedTransmitPoint(string transmitPointName)
    {
        _loadedTransmitPoints.TryGetValue(transmitPointName, out var obj);
        return obj;
    }

    private class LoadedItem
    {
        public GameObject instance;
        public Vector3 position;
    }

    /// <summary>
    /// 附近格子信息的结果类
    /// </summary>
    public class NearbyGridInfo
    {
        public Dictionary<Vector2Int, MapOtherUnitInfoPack> otherUnits = new Dictionary<Vector2Int, MapOtherUnitInfoPack>();
        public Dictionary<Vector2Int, MapBlock> mapBlocks = new Dictionary<Vector2Int, MapBlock>();
    }
}