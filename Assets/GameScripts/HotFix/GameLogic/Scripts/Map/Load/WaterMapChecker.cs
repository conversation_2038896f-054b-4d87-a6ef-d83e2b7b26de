using System.Collections.Generic;
using UnityEngine;
using Newtonsoft.Json;
using System;

/// <summary>
/// 加载水域 JSON 文件，并判断世界坐标是否在水中（包含 Y 高度判断）
/// </summary>
public class WaterMapChecker
{
    private class BlockInfo
    {
        public string BlockName;
        public Vector3 WorldOrigin;
        public Dictionary<Vector2Int, float> WaterDict; // 存储每个格子的水面高度
        public Dictionary<Vector2Int, bool> WaterIsOnDict;
    }
    public class WaterInfo
    {
        public Vector3 Pos;
        public Vector3 WordPos;
        public bool IsWaterEdge;
    }

    private List<BlockInfo> blocks = new();
    private int gridSize;
    private float cellSize;
    // private Transform _parantTransform;

    private Dictionary<Vector2Int, BlockInfo> blockMap = new();
    private Vector2Int tempCell = new();
    // private WaterMapData _waterMapData;
    // public WaterMapChecker(WaterMapData waterMapData)
    // {
    //     // _parantTransform = mapRoot;
    //     _waterMapData = waterMapData;
    //     Parser(waterMapData);
    //     // foreach (var pathName in jsonPathNames)
    //     // {
    //     //     var json = AssertResourceLoader.Share.LoadAsset<TextAsset>(pathName).text;
    //     //     Parser(json, mapRoot);
    //     // }
    // }

    // public void Parser(WaterMapData waterMapData) //Transform mapRoot, 
    // {
    //     // var data = JsonConvert.DeserializeObject<WaterMapData>(jsonText);
    //     var data = waterMapData;
    //     gridSize = data.gridSize;
    //     cellSize = data.cellSize;

    //     blocks.Clear();
    //     blockMap.Clear();

    //     foreach (var block in data.blocks)
    //     {
    //         // Vector3 worldPos = mapRoot.TransformPoint(block.localPosition);
    //         Vector3 worldPos = block.localPosition;
    //         // Vector3 worldOrigin = WaterWordOrigin(worldPos, new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize));
    //         // if(worldOrigin.x > 0) {
    //         //     worldOrigin.x = worldOrigin.x - gridSize / 2f * cellSize;
    //         // } else {
    //         //     worldOrigin.x = worldOrigin.x + gridSize / 2f * cellSize;
    //         // }
    //         // if(worldOrigin.z > 0) {
    //         //     worldOrigin.z = worldOrigin.z - gridSize / 2f * cellSize;
    //         // } else {
    //         //     worldOrigin.z = worldOrigin.z + gridSize / 2f * cellSize;
    //         // }
            
    //         Vector3 worldOrigin = worldPos - new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize);
            
    //         var dict = new Dictionary<Vector2Int, float>();
    //         var isOnDict = new Dictionary<Vector2Int, bool>();
    //         foreach (var cell in block.waterCells)
    //         {
    //             dict[cell.coord] = cell.surfaceY;
    //             isOnDict[cell.coord] = cell.isWaterEdge;
    //         }

    //         var blockInfo = new BlockInfo
    //         {
    //             BlockName = block.blockName,
    //             WorldOrigin = worldOrigin,
    //             WaterDict = dict,
    //             WaterIsOnDict = isOnDict
    //         };
    //         int blockX = Mathf.FloorToInt(worldPos.x / (gridSize * cellSize));
    //         int blockZ = Mathf.FloorToInt(worldPos.z / (gridSize * cellSize));
    //         // int blockX = GetBlockValue(worldPos.x / (gridSize * cellSize));//Mathf.FloorToInt(worldPos.x / (gridSize * cellSize));
    //         // int blockZ = GetBlockValue(worldPos.z / (gridSize * cellSize), true);//Mathf.FloorToInt(worldPos.z / (gridSize * cellSize));
    //         var blockKey = new Vector2Int(blockX, blockZ);

    //         blocks.Add(blockInfo);
    //         blockMap[blockKey] = blockInfo;
    //     }
    // }
    // private int GetBlockValue(float value, bool floor = false) {
    //     // if(value > 0) {
    //     //     return (int)Mathf.Round(Math.Abs(value));
    //     // } else {
    //     //     return -(int)Mathf.Round(Math.Abs(value));
    //     // }
    //     return (int)Mathf.Round(Math.Abs(value));
    //     // if(floor) {
    //     //     return Mathf.FloorToInt(Math.Abs(value));
    //     // } else {
    //     //     return Mathf.CeilToInt(Math.Abs(value));
    //     // }
    //     // if(floor) {
    //     //     return value < 0 ? Mathf.CeilToInt(value) : Mathf.FloorToInt(value);
    //     // }
    //     // return value > 0 ? Mathf.CeilToInt(value) : Mathf.FloorToInt(value);
    // }
    // private int GetWaterGridValue(float value) {
    //     //水的坐标是0开始的，另外因为有180度旋转，整个坐标需要微调
    //     // if(value > 0) {
    //     //     return (int)Mathf.FloorToInt(Math.Abs(value));
    //     // } else {
    //     //     return -(int)Mathf.FloorToInt(Math.Abs(value));
    //     // }
    //     return (int)Mathf.FloorToInt(Math.Abs(value));
    // }
    //获取水面的cell格子（32*32的位置）
    public WaterInfo? GetWaterInfo(Vector3 worldPos) {
        // Vector3 worldOrigin = _parantTransform.InverseTransformPoint(worldPos) - new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize);
        // worldPos = _parantTransform.InverseTransformPoint(worldPos);
        // worldPos = worldPos - new Vector3(gridSize / 2f * cellSize, 0, gridSize / 2f * cellSize);
        float worldBlockSize = gridSize * cellSize;
        int blockX = Mathf.RoundToInt(worldPos.x / worldBlockSize);
        // if(blockX < 0) {
        //     blockX = -Mathf.FloorToInt(Math.Abs(worldPos.x / worldBlockSize));
        // }
        int blockZ = Mathf.RoundToInt(worldPos.z / worldBlockSize);
        // int blockX = GetBlockValue(worldPos.x / worldBlockSize);
        // int blockZ = GetBlockValue(worldPos.z / worldBlockSize);
        var blockKey = new Vector2Int(blockX, blockZ);

        if (blockMap.TryGetValue(blockKey, out var block))
        {
            Vector3 local = worldPos - block.WorldOrigin;

            int x = Mathf.FloorToInt(local.x / cellSize);
            int z = Mathf.FloorToInt(local.z / cellSize);
            // int x = GetWaterGridValue(local.x / cellSize);
            // int z = GetWaterGridValue(local.z / cellSize);

            if ((uint)x < gridSize && (uint)z < gridSize)
            {
                tempCell.x = x;
                tempCell.y = z;

                if (block.WaterDict.TryGetValue(tempCell, out float surfaceY))
                {
                    WaterInfo info = new WaterInfo();
                    info.Pos = new Vector3(x, surfaceY, z);
                    Vector3 cellCenterWorldPos = block.WorldOrigin + new Vector3((x + 0.5f) * cellSize, surfaceY, (z + 0.5f) * cellSize);
                    // z = block.WorldOrigin.z > 0 ? z : -z;
                    // Vector3 cellCenterWorldPos = block.WorldOrigin + new Vector3((x + 0.5f) * cellSize, surfaceY, (block.WorldOrigin.z > 0 ? z + 0.5f : z - 0.5f) * cellSize);
                    info.WordPos = cellCenterWorldPos;
                    info.IsWaterEdge = block.WaterIsOnDict[tempCell];
                    return info;
                    // new Vector3(x, surfaceY, z);
                    // z = block.WorldOrigin.z > 0 ? z : -z;
                    // Vector3 cellCenterWorldPos = block.WorldOrigin + new Vector3((x + 0.5f) * cellSize, surfaceY, (z + 0.5f) * cellSize);
                    // // return Mathf.Abs(worldPos.y - surfaceY) <= 10f;
                    // return new Vector3(x, surfaceY, z);
                }
            }
        }

        return null;
    }
    public static Vector3 WaterWordOrigin(Vector3 pos, Vector3 changePos) {
        if(pos.x > 0) {
            pos.x = pos.x - changePos.x;
        } else {
            pos.x = pos.x + changePos.x;
        }
        if(pos.y > 0) {
            pos.y = pos.y - changePos.y;
        } else {
            pos.y = pos.y + changePos.y;
        }
        if(pos.z > 0) {
            pos.z = pos.z - changePos.z;
        } else {
            pos.z = pos.z + changePos.z;
        }
        return pos;
    }
    //获取水面格子的世界坐标
    // public WaterInfo? GetWaterWorldPos(Vector3 worldPos) {
    //     float worldBlockSize = gridSize * cellSize;
    //     // int blockX = Mathf.FloorToInt(worldPos.x / worldBlockSize);
    //     // int blockZ = Mathf.FloorToInt(worldPos.z / worldBlockSize);
    //     int blockX = GetBlockValue(worldPos.x / worldBlockSize);
    //     int blockZ = GetBlockValue(worldPos.z / worldBlockSize);
    //     var blockKey = new Vector2Int(blockX, blockZ);

    //     if (blockMap.TryGetValue(blockKey, out var block))
    //     {
    //         Vector3 local = worldPos - block.WorldOrigin;

    //         // int x = Mathf.FloorToInt(local.x / cellSize);
    //         // int z = Mathf.FloorToInt(local.z / cellSize);
    //         int x = GetWaterGridValue(local.x / cellSize);
    //         int z =  GetWaterGridValue(local.z / cellSize);
    //         // int x = GetBlockValue(local.x / cellSize)  - 1;
    //         // int z = GetBlockValue(local.z / cellSize, true)  - 1;

    //         if ((uint)x < gridSize && (uint)z < gridSize)
    //         {
    //             Vector2Int cellCoord = new Vector2Int(x, z);

    //             if (block.WaterDict.TryGetValue(cellCoord, out float surfaceY))
    //             {
    //                 z = block.WorldOrigin.z > 0 ? z : -z;
    //                 Vector3 cellCenterWorldPos = block.WorldOrigin + new Vector3((x + 0.5f) * cellSize, surfaceY, (z + 0.5f) * cellSize);
    //                 return cellCenterWorldPos;
    //             }
    //         }
    //     }

    //     return null;
    // }
    /// <summary>
    /// 判断给定世界坐标是否在水面（±10m 内）
    /// </summary>
    public bool IsWater(Vector3 worldPos)
    {
        var info = GetWaterInfo(worldPos);
        if(info != null) {
            return Mathf.Abs(worldPos.y - info.WordPos.y) <= 10f;
        }
        // float worldBlockSize = gridSize * cellSize;

        // int blockX = Mathf.FloorToInt(worldPos.x / worldBlockSize);
        // int blockZ = Mathf.FloorToInt(worldPos.z / worldBlockSize);
        // var blockKey = new Vector2Int(blockX, blockZ);

        // if (blockMap.TryGetValue(blockKey, out var block))
        // {
        //     Vector3 local = worldPos - block.WorldOrigin;

        //     int x = Mathf.FloorToInt(local.x / cellSize);
        //     int z = Mathf.FloorToInt(local.z / cellSize);

        //     if ((uint)x < gridSize && (uint)z < gridSize)
        //     {
        //         tempCell.x = x;
        //         tempCell.y = z;

        //         if (block.WaterDict.TryGetValue(tempCell, out float surfaceY))
        //         {
        //             return Mathf.Abs(worldPos.y - surfaceY) <= 10f;
        //         }
        //     }
        // }

        return false;
    }

    // public bool IsWater(Transform transform)
    // {
    //     return IsWater(transform.position);
    // }
    // public bool IsLand(Transform transform) {
    //     if (!NavMesh.SamplePosition(transform.position, out NavMeshHit hit, 0.1f, NavMesh.AllAreas))
    //     {
    //         return false;
    //     }
    //     var info = GetWaterInfo(transform.position);
    //     if(info != null) {
    //         return info.IsWaterEdge;
    //     }

    //     return true;
    // }
    public bool IsLand(Vector3 worldPos) {
        var info = GetWaterInfo(worldPos);
        if(info != null) {
            return info.IsWaterEdge;
        }

        return true;
    }
    // public WaterInfo? GetWaterWorldPos(Transform transform) {
    //     return GetWaterInfo(transform.position);
    // }
    public WaterInfo? GetWaterWorldPos(Vector3 worldPos) {
        return GetWaterInfo(worldPos);
    }
    // public Vector3? GetNavigableWaterCellPosition(Transform transform) {

    // }
    // public bool TryMoveToNavigableWaterCell(Transform transform, float navSampleMaxDistance = 1f)
    // {
    //     var position = GetWaterWorldPos(transform.position);
    //     if(position != null) {
    //         // int x = Mathf.FloorToInt(position.Value.x);
    //         // int z = Mathf.FloorToInt(position.Value.z);
    //         // float surfaceY = position.Value.y;
    //         // Vector3 cellCenterWorldPos = block.WorldOrigin + new Vector3((x + 0.5f) * cellSize, surfaceY, (z + 0.5f) * cellSize);
    //         // 检查是否在 NavMesh 上可行走
    //         if (NavMesh.SamplePosition(position.Value, out NavMeshHit hit, navSampleMaxDistance, NavMesh.AllAreas))
    //         {
    //             transform.position = hit.position;
    //             return true;
    //         }
    //     }
    //     // Vector3 worldPos = transform.position;

    //     // float worldBlockSize = gridSize * cellSize;
    //     // int blockX = Mathf.FloorToInt(worldPos.x / worldBlockSize);
    //     // int blockZ = Mathf.FloorToInt(worldPos.z / worldBlockSize);
    //     // var blockKey = new Vector2Int(blockX, blockZ);

    //     // if (blockMap.TryGetValue(blockKey, out var block))
    //     // {
    //     //     Vector3 local = worldPos - block.WorldOrigin;

    //     //     int x = Mathf.FloorToInt(local.x / cellSize);
    //     //     int z = Mathf.FloorToInt(local.z / cellSize);

    //     //     if ((uint)x < gridSize && (uint)z < gridSize)
    //     //     {
    //     //         Vector2Int cellCoord = new Vector2Int(x, z);

    //     //         if (block.WaterDict.TryGetValue(cellCoord, out float surfaceY))
    //     //         {
    //     //             Vector3 cellCenterWorldPos = block.WorldOrigin + new Vector3((x + 0.5f) * cellSize, surfaceY, (z + 0.5f) * cellSize);

    //     //             // 检查是否在 NavMesh 上可行走
    //     //             if (NavMesh.SamplePosition(cellCenterWorldPos, out NavMeshHit hit, navSampleMaxDistance, NavMesh.AllAreas))
    //     //             {
    //     //                 transform.position = hit.position;
    //     //                 return true;
    //     //             }
    //     //         }
    //     //     }
    //     // }

    //     return false;
    // }


    // [System.Serializable]
    // public class WaterMapData
    // {
    //     public int GridSize { get; set; }
    //     public float CellSize { get; set; }
    //     public List<WaterMapBlock> Blocks { get; set; }
    // }

    // [System.Serializable]
    // public class WaterMapBlock
    // {
    //     public string BlockName { get; set; }
    //     public Vector3 LocalPosition { get; set; }
    //     public List<WaterCell> WaterCells { get; set; }
    // }

    // [System.Serializable]
    // public class WaterCell
    // {
    //     public Vector2Int Coord { get; set; }
    //     public float SurfaceY { get; set; }
    // }
}
