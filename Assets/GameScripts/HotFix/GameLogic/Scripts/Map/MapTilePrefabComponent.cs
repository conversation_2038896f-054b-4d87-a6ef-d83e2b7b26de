using System.Text.RegularExpressions;
using UnityEngine;

[DisallowMultipleComponent]
public class MapTilePrefabComponent : MonoBehaviour
{
    public string Bgm;
    public string RegionId;
    public string LocationId;
    public string AreaId;
    public string BattleBgm;
    public bool IsBattle = false;
    public bool IsOut = false;
    public MapTileType TileType = MapTileType.Grass;
    // public void Init(PrefabMapTileInfo info)
    // {
    //     Bgm = Regex.Replace(info.Bgm, "[^a-zA-Z0-9]", "");
    //     RegionId = info.RegionId;
    //     AreaId = info.AreaId;
    //     BattleBgm = Regex.Replace(info.BattleBgm, "[^a-zA-Z0-9]", "");
    //     IsBattle = info.IsBattle;
    //     TileType = info.TileType;
    //     // if(info.Walkable) {
    //     //     this.gameObject.AddComponent<MeshCollider>();
    //     // }
    // }
}

public enum MapTileType {
    Grass,
    Forest,
    Water,
    Rock,
    Snow,
    Desert,
    Cave,
    Building,
    Other
}