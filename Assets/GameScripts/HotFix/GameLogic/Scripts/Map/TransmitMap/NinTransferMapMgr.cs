using UnityEngine;
using Cysharp.Threading.Tasks;
using System;
public class NinTransferMapMgr
{
    public MapLoader mapLoader;
    public NinTransferMapMgr(MapLoader mapLoader)
    {
        this.mapLoader = mapLoader;
    }
    public Vector3 TransformPoint(Vector3 position)
    {
        var pos = MapController.Current.mapCharacterMgr.characterContainer.TransformPoint(mapLoader.transform.TransformPoint(position));
        return pos;
    }
    public NinMapPoint GetInitMapPoint(string? tagName = null, IMapInfo? mapInfo = null)
    {
        if (mapInfo == null)
        {
            mapInfo = mapLoader.GetMapInfo();
        }
        var initPosition = mapInfo.GetDefaultInitPosition();
        if (!string.IsNullOrEmpty(tagName))
        {
            initPosition = mapInfo.GetInitPosition(tagName);
        }
        NinMapPoint ninMapPoint = new NinMapPoint
        {
            position = TransformPoint(initPosition.position),
            mapInfo = mapInfo
        };
        return ninMapPoint;
    }
    public void MoveToCurrentMap(NinChatacterTransfer chatacterTransfer, Vector3 point)
    {
        chatacterTransfer.Transfer(point).Forget();
    }
    public async UniTask MoveToCurrentMap(NinChatacterTransfer chatacterTransfer, string transmitName)
    {
        await MoveByMapInfo(chatacterTransfer, mapLoader.GetMapInfo(), transmitName);
    }
    public async UniTask MoveToCurrentMap(NinChatacterTransfer chatacterTransfer, Vector3 point, bool isSyncLoadMap = true)
    {
        await chatacterTransfer.Transfer(point);
        if (isSyncLoadMap)
        {
            await mapLoader.AsyncLoadMap(point);
        }
    }
    public async UniTask MoveNewMapBefore(IMapInfo mapInfo) {
        MapController.Current.yarnMgr.SetDefaultYarnProject(mapInfo.GetMapNameId());
        // MapTransitionAnimator.ToMapPoint();
        // await UniTask.Delay(100);
        mapLoader.SetMapInfo(mapInfo);
    }
    public async UniTask MoveNewMapAfter(IMapInfo mapInfo) {
        var instanceMapInfo = mapInfo as InstanceMapInfo;
        if (instanceMapInfo != null)
        {
            if (instanceMapInfo.trainerQuest != null)
            {
                await MapController.Current.yarnMapMgr.LoadInstancMapInfo(instanceMapInfo);
            }
            else
            {
                await MapController.Current.yarnMapMgr.LoadInstancMapInfo(null);
            }
        }
    }
    public async UniTask MoveToNewMap(NinChatacterTransfer chatacterTransfer, IMapInfo mapInfo, Vector3 point, bool isSyncLoadMap = true)
    {
        await MoveNewMapBefore(mapInfo);
        await MoveToCurrentMap(chatacterTransfer, point, isSyncLoadMap);
        await MoveNewMapAfter(mapInfo);
    }
    public async UniTask MoveToNewMap(NinChatacterTransfer chatacterTransfer, IMapInfo mapInfo, string? transmitName = null)
    {
        await MoveNewMapBefore(mapInfo);
        await MoveByMapInfo(chatacterTransfer, mapInfo, transmitName);
        await MoveNewMapAfter(mapInfo);
    }
    //transmitName 可以是initpoint的名称
    private async UniTask MoveByMapInfo(NinChatacterTransfer chatacterTransfer, IMapInfo mapInfo, string? transmitName = null)
    {
        // var mapInfo = MainMapInfo.Create(mainLandType);
        // if(mapInfo == null) {
        //     Debug.LogError("无法获取mapInfo");
        //     return;
        // }

        if (string.IsNullOrEmpty(transmitName))
        { //没有传送点使用默认传送
            var initPosition = mapLoader.GetMapInfo().GetDefaultInitPosition();
            var pos = MapController.Current.mapCharacterMgr.characterContainer.TransformPoint(mapLoader.transform.TransformPoint(initPosition.position));
            await chatacterTransfer.Transfer(pos);
            await mapLoader.AsyncLoadMap(pos);
        }
        else
        {
            // 获取目标地图的传送点信息
            var targetTransmitPoint = mapLoader.GetTransmitPointInfo(transmitName);
            if (targetTransmitPoint == null)
            {
                Debug.LogError($"No transmit point found in target map: {transmitName}");
                return;
                // yield break;
            }
            // var transfer = mapCharacterMgr.myCharacter.ninChatacterTransfer;
            // 获取目标传送点信息
            MapTransmitPointInfoPack targetInfo = targetTransmitPoint;

            // 计算目标位置的世界坐标
            // 使用当前传送点的父物体（地图容器）的Transform来计算
            Transform mapContainer = mapLoader.transform;
            if (mapContainer == null)
            {
                Debug.LogError("Map container not found!");
                return;
                // yield break;
            }

            // 将目标本地坐标转换为世界坐标（使用与当前传送点相同的转换方式）
            Vector3 targetWorldPosition = mapContainer.TransformPoint(targetInfo.position);
            chatacterTransfer.SetTransferringMap(transmitName);
            await MoveToCurrentMap(chatacterTransfer, targetWorldPosition, true);
        }
        // var instanceMapInfo = mapInfo as InstanceMapInfo;
        // if (instanceMapInfo != null)
        // {
        //     if (instanceMapInfo.trainerQuest != null)
        //     {
        //         await MapController.Current.yarnMapMgr.LoadInstancMapInfo(instanceMapInfo);
        //     }
        //     else
        //     {
        //         await MapController.Current.yarnMapMgr.LoadInstancMapInfo(null);
        //         // MapController.Current.yarnMgr.LoadYarnProjectIfNeed(instanceMapInfo.GetMapNameId());
        //         // MapController.Current.yarnMgr.LoadTitle("Test", "storm_intro");
        //     }
        // }
        // chatacterTransfer.TransferA(targetWorldPosition, (isComplete) => {
        //     if(isComplete) {
        //         mapLoader.LoadMap(targetWorldPosition);
        //         // mapLoader.LoadNearby(targetWorldPosition);
        //     }
        //     completeAction(isComplete);
        // });
        // var transmitPoint = mapLoader.GetLoadedTransmitPoint(transmitName);
        // if(transmitPoint == null) {
        //     Debug.LogError("无法获取transmitPoint");
        //     return;
        // }
        // var transmitComponent = transmitPoint.GetComponent<TransmitMapComponent>();
        // if(transmitComponent == null) {
        //     Debug.LogError("无法获取transmitComponent");
        //     return;
        // }
        // await transmitComponent.StartTransfer(mapCharacterMgr.myCharacter.ninChatacterTransfer);
    }
}