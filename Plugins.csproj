﻿<Project ToolsVersion="Current">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp/obj/$(Configuration)/$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>9.0</LangVersion>
    <Configurations>Debug;Release</Configurations>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Plugins</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp/bin/Debug/</OutputPath>
    <DefineConstants>UNITY_2022_3_52;UNITY_2022_3;UNITY_2022;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION;ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT;ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE;PLATFORM_IOS;TEXTCORE_1_0_OR_NEWER;ENABLE_RUNTIME_GI;ENABLE_GAMECENTER;ENABLE_NETWORK;ENABLE_IOS_ON_DEMAND_RESOURCES;ENABLE_IOS_APP_SLICING;PLAYERCONNECTION_LISTENS_FIXED_PORT;DEBUGGER_LISTENS_FIXED_PORT;PLATFORM_SUPPORTS_ADS_ID;SUPPORT_ENVIRONMENT_VARIABLES;PLATFORM_SUPPORTS_PROFILER;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_ETC_COMPRESSION;UNITY_IPHONE_API;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;UNITY_IOS;PLATFORM_IPHONE;UNITY_IPHONE;UNITY_HAS_GOOGLEVR;ENABLE_SPATIALTRACKING;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_OSX;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;DOTWEEN;MOREMOUNTAINS_INTERFACE;MOREMOUNTAINS_TOPDOWNENGINE;MOREMOUNTAINS_INVENTORYENGINE;COZY_WEATHER;COZY_3_AND_UP;COZY_URP;TextMeshPro;ENABLE_LOG;ENABLE_HYBRIDCLR;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp/bin/Release/</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.22</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>iOS:9</UnityBuildTarget>
    <UnityVersion>2022.3.52f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="/Users/<USER>/.vscode/extensions/visualstudiotoolsforunity.vstuc-1.1.2/Analyzers/Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="/Users/<USER>/Documents/Unity/PokemonUnity/Pets_SanGuo/Library/PackageCache/dev.yarnspinner.unity@be66087b21/SourceGenerator/YarnSpinner.Unity.SourceCodeGenerator.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="/Users/<USER>/Documents/Unity/PokemonUnity/Pets_SanGuo/Assets/TEngine/Runtime/Core/GameEvent/SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Quest/UnityUIQuestTemplateAlternateDescriptions.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Selector/SelectorUseStandardUIElements.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Utility/DatabaseMerger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/ShowAlert/ShowAlertBehaviour.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Save System/DialogueSystemSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/PreloadActorPortraits.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/Gallery/GalleryHorizontalDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Misc/AutoSaveLoad.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelSpecialDelete.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaLibrary/IOLib.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/NestedItemData.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Dialogue/BringSubtitlePanelToFrontOnFocus.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Utility/PreloadActorPortraits.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/ForInStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Abstract/Dialogue/AbstractUISubtitleControls.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Toggle/ToggleText.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommand.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/AddAnimationItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Spawning/SpawnedObject.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/BarkOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/DraggableHorizonalItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/BitMaskAttribute.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Quest/StandardUIQuestLogWindow.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/DialogueEntry.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Selector/StandardUISelectorElements.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Slider/SliderTransition.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Celtx3/CeltxCondition.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/ArticyTools.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Extra/Tuple.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Logic/DB/DatabaseManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Quest/UnityUIQuestTemplateAlternateDescriptions.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/Gallery/GalleryVerticalDemoScript.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/DraggableView/DraggableViewTopToBottomDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Dialogue/StandardDialogueUI.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowMultiButton.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/EntryGroup.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Misc/SaveSystemMethods.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Common/TransitionClick.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/SequencerTools.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Demo/DemoMenu.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Toast/ToastUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaValue/LuaFunction.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Notification/NotificationContentFitter.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandAnimation.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/ListView/LoopListViewItem2.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Effects/FlashEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Demo Scripts/SimpleController.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UnityEvents/TriggerEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/BreakStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/TextInput.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/NestedSimpleGridViewTopBottomItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Save System/PersistentActiveData.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Demo Scripts/SmoothCameraWithBumper.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/MVC/Actor/ActorSubtitleColor.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Utility/LuaNetworkCommands.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Scripts/Editor/CustomFieldType_Conversation.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowCheckBoxUI.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Button/ButtonMultiTransition.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/GridView/LoopGridItemPool.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/QuestState/SetQuestStateClip.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/ShowAlert/AlertMixerBehaviour.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/Conversation/StartConversationBehaviour.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/LuaOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Base/DragChangSizeScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/Bark/BarkTrack.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/UnityUIResponseMenuControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/Assignment.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedSimpleListViewDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/MVC/Model/DialogueSystemSceneEvents.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/RepeatStmt.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/ToggleItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/SequenceParser.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Quests/QuestStateIndicator.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Quest/UnityUIQuestTitle.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Misc/EnableOnStart.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Selector/UnityUISelectorDisplay.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowListUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/DictionaryExtensions.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Savers/MultiActiveSaver.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewSimpleLoadMoreDemo.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Misc/SaveSystemTestMenu.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Quest/UnityUIQuestGroupTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/Condition.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Editor/CustomLuaFunctionInfo.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Text/StringAsset.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Abstract/Bark/AbstractBarkUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/SequencerKeywords.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/SequencerMessage/SequencerMessageMixerBehaviour.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Celtx/CeltxData.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Common/TransitionMultiDown.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/GUIScrollView.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Attributes/QuestEntryPopupAttribute.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Utility/ConversationLogger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/Continue/ContinueConversationClip.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Effects/UpdateLocalizedUITexts.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Asset.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanel.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Misc/LODManager.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/NestedSimpleLeftRightItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/LocalizeUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Quests/QuestStateListener.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/ShowAlert/ShowAlertClip.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ContextMenu/ContextMenu.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewSimpleLoopDemoScript.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Tooltip/Tooltip.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/DialogueSystemTriggerEvent.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Slider/SliderRange.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/LocalizedFonts.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaInterpreter.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Localization Import/DialogueDatabaseLocalizationImporter.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Savers/MultiEnabledSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Options/Cinemachine/CinemachineCameraPriorityOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/StandardUISubtitleControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Selector/StandardUISelectorElements.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/Conversation/StartConversationClip.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/UIAutonumberSettings.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/ScaledRect/ScaledRect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Utility/UnityUIColorText.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Logic/Shared/Subtitle.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewSimpleLeftToRightDemoScript.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewLeftToRightDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/BaseExpr.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/TableConstructor.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/ToggleItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Scripts/TemplateDialogueUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/UnityUITimer.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedGridViewLeftToRightDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/InputDeviceManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/GameTime.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/View/Dialogue/SelectedResponseEventArgs.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Manager/LuaWatchers.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Misc/SaveSystemEvents.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Selector/UnityUISelectorElements.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewFeatureDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/SetAnimationOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/StandardUIDialogueControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Attributes/ItemPopupAttribute.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Selector/UsableUnityUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/ConversionSetting.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Savers/ActiveSaver.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/ListView/LoopListItemPool.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Base/DragEventHelper.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/LuaTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/ForStmt.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/ExpandAnimationType.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Quests/QuestState.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Text/GlobalTextTable.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Demo/DieOnTakeDamage.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/UITextColor.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/ConditionPriority.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/ForStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Controller/ConversationController.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/Statement.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/Chunk.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/TextMeshProTypewriterEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/Schemas/Articy 1.4/XmlContentExport_FullProject_1_4.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Dialogue/StandardUIResponseButton.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelSpecialLoad.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/Schemas/Articy 2.2/Articy_2_2_Tools.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Actor/ActorSubtitleColor.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Field.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarSpecial.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/DraggableView/DraggableViewLeftToRightDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/InputDeviceManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Quests/QuestStateIndicator.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/SetAnimationOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewRightToLeftDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/MVC/Actor/OverrideActorName.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Tools/Fit.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/Schemas/Articy 3.1/Articy_3_1_Tools.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/View/Bark/BarkDialogueUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/ConversationPositionStack.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandAnimatorLayer.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedGridViewTopToBottomDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/ConverterPrefs.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/Legacy/BarkTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/GroupExpr.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/ArticyData.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/ReturnStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Actor/Override/OverrideDisplaySettings.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Quest/UnityUIQuestTrackTemplate.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewPullUpLoadMoreDemo.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindow.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Utility/CharacterTypeUtility.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Common/TransitionMultiHover.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaLibrary/TableLib.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Quests/QuestLogWindowHotkey.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Localized Text/LocalizedTextTable.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Storers/DiskSavedGameDataStorer.cs" />
    <Compile Include="Assets/Plugins/DOTween/Modules/DOTweenModuleUtils.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/QueuedSequencerCommand.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarSpecialPattern.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Quest/Common/StandardUIContentTemplate.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Common/TransitionMultiClick.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/LocalVar.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/SimpleItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/RepeatStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Save System/PersistentActiveDataMultiple.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/FunctionValue.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Dialogue/UnityUIDialogueUI.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/StopConversationIfTooFar.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Transitions/StandardSceneTransitionManager.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/SliderComplexItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Save System/PersistentPositionData.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Manager/DialogueSystemEvents.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Quest/StandardUIQuestTrackTemplate.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/NestedTopBottomItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/Legacy/ConversationTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Item.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/UnityUIIgnorePauseCodes.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Dialogue/UnityUIResponseButton.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/FlashEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Quest/Common/StandardUITextTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Misc/SaveSystemEvents.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Demo Scripts/DieOnTakeDamage.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Misc/SceneValidationMode.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Toast/ToastContentFitter.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Quest/Common/StandardUIToggleTemplate.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/PageView/PageViewSimpleDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/UIAnimatorMonitor.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarPatternCircular.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/CustomLuaFunctionInfo.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/UnityBarkUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaValue/LuaError.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/UnityTextFieldUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Logic/Shared/Response.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandLookAt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/GUIVisibleControl.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/GUITextField.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/AlwaysFaceCamera.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/InputFieldItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Save System/ConversationStateSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandAudioWWW.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/SlideEffect.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Base/AnimationHelper.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/Utility/PreviewUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Storers/PlayerPrefsSavedGameDataStorer.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Attributes/QuestStateAttribute.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/StaggeredView/StaggeredViewLeftToRightDemoScript.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarPattern.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Tools/ImageAnimation.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/UILocalizationManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Text/TextTable.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowContentFitterMultiButton.cs" />
    <Compile Include="Assets/Plugins/FantaziaCharacterEditor/Scripts/InventoryIconsManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandSetTimeout.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/Legacy/PauseGameOnConversation.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowRadioButton.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/SceneNotifier.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/ConversionSettings.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/RunLua/RunLuaMixerBehaviour.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Arcweave/ArcweaveLua.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Savers/Saver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Utility/LuaWatchList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/PlaySequence/PlaySequenceClip.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/Legacy/SequenceTrigger.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/TreeViewItemData.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/ToggleRowColItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Scripts/TemplateBarkUI.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/TreeViewItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/KeepRectTransformOnscreen.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Dialogue/OverrideUnityUIDialogueControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/Portraits/AnimatedPortrait.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Editor/Text/StringAssetMenu.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Save System/ConversationStateSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Attributes/ActorPopupAttribute.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/Schemas/Articy 3.1/XmlContentExport_FullProject_3_1.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Save System/PersistentActiveData.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/Continue/ContinueConversationBehaviour.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarPatternCircularAuto.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowProgressBarLoop.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Logic/DB/DatabaseResetOptions.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarSpecialPatternAuto.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/ActOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaValue/LuaTable.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/DialogueDatabase.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Quest/Common/StandardUIToggleTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/LocalFunc.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/SliderItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/MVC/Actor/BarkGroupMember.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Selector/UnityUISelectorElements.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/VariableArg.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/KeyAccess.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/Pool.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/SendMessageOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/IfStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/Schemas/Articy 1.4/Articy_1_4_Tools.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/ArticyConverter.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/BringSubtitlePanelToFrontOnFocus.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Actor/OverrideActorName.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedSimpleGridViewDemoScript.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewSelectDeleteDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/EncodingType.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/ConversationTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Attributes/LocationPopupAttribute.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/UIUtility.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Base/DragEventForward.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/PlaySequence/PlaySequenceMixerBehaviour.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/StandardUIAlertControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Transitions/StandardSceneTransitionManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Attributes/DialogueEntryPopupAttribute.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/BaseHorizontalToggleItemList.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarCircularMoveAuto.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Common/TransitionDown.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Utility/Template.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Notification/NotificationContentFitterUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/Operation.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/Legacy/LuaTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Triggers/Interaction/Selector.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Triggers/Trigger/DialogueSystemTrigger.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/TreeViewItemHead.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Selector/StandardUsableUI.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/DescList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Savers/AnimatorSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Attributes/DialogueSystemTriggerEventAttribute.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/AlwaysFaceCamera.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/Common/CommonDefine.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Manager/DialogueSystemEvents.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Dialogue/StandardUIInputField.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Manager/DialogueSystemMessages.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Tools.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UnityEvents/TimedEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Dialogue/StandardUIMenuPanel.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Utility/LuaWatchItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Serializers/DataSerializer.cs" />
    <Compile Include="Assets/Plugins/FantaziaCharacterEditor/Scripts/GearEquipper.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Chat Mapper/ChatMapperUtility.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/Schemas/Articy 2.4/XmlContentExport_FullProject_2_4.cs" />
    <Compile Include="Assets/Plugins/DOTween/Modules/DOTweenModuleAudio.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Quest/StandardUIQuestTracker.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Importers/Articy/ArticyLuaFunctions.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/PlaySequence/PlaySequenceTrack.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/UnityUIRoot.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/GUIProgressBar.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/GUILabel.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/MVC/View/SequencerShortcuts.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/ItemDataBase.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarSpecialAuto.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/VarName.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/Panel Numbers/SubtitlePanelNumber.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/ExprStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/ShowCursorWhileEnabled.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UnityEvents/TriggerEvent.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Notification/NotificationUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/RectExtensions.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Save System/PersistentPositionData.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/OperTable.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/Args.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/TreeView/TreeViewDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Quest/UnityUIQuestTracker.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Wrapper/Lua Interpreter/LuaTableWrapper.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Gradient/GradientModifier.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Bark/StandardBarkUI.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarCircularRoundAuto.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewTopToBottomDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Quest/StandardUIQuestTrackTemplate.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowContentFitterMultiButtonUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/UnityUIResponseButton.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/Continue/ContinueConversationTrack.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/Continue/ConversationContinueMixerBehaviour.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/TabView/TabSimple.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Bark/UnityUIBarkUI.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/SimpleExpandItemData.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/SimpleLoadItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/MVC/Actor/BarkGroupManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UnityEvents/DisappearEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/DoStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/Dialogue/Controls/UnityQTEControls.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/NestedGridViewTopBottomItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/LocalizedFonts.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelGridView.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelMenuList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Save System/DialogueSystemSaver.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/BaseVerticalItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaValue/LuaString.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/ChatViewItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/UITextColor.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Res/ResManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/SetAnimatorStateOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Misc/AutoSaveLoad.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Effects/TypewriterEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Cinemachine/Sequencer Commands/SequencerCommandCinemachineTarget.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/SequencerCommandGroupAttribute.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/TreeView/TreeViewWithStickyHeadDemoScript.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/DraggableItemData.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/TypewriterEffect.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Notification/NotificationContentFitterWithButtonUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Utility/AlwaysFaceCamera.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/GUITextField.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/Var.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Wrapper/NLua/NLua_LuaTableWrapper.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/SequencerMessage/SequencerMessageBehaviour.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Tooltip/TooltipSnap.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/InputField/InputFieldTransition.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/Assignment.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/MethodCall.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Cinemachine/Sequencer Commands/SequencerCommandCinemachineZoom.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Actor/DefaultCameraAngle.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/KeyAccess.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowInputField.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/InputDeviceMethods.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Tooltip/TooltipDetermine.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Effects/ContinueButtonFastForward.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Scripts/Editor/ConverterWindowTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Misc/AlwaysFaceCamera.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaLibrary/MathLib.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewExpandDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Actor/DialogueActor.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Dropdown/DropdownTransition.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/ScaledRect/ScaledRectAlignment.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Quests/QuestLogWindow.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/ConditionObserver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UnityEvents/TimedEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Dialogue/Portraits/AnimatedPortrait.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewClickLoadMoreDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Celtx/CeltxDataRaw.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/SetEnabledOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/Term.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/InputField/InputFieldTransitionSpecial.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/GUIRoot.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandAudioWait.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/TimerEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/SequencerMessage/SequencerMessageTrack.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/SetAnimatorStateOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/MoreGizmos.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Twine/TwineImporter.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/InputDeviceMethods.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Effects/StandardUIContinueButtonFastForward.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Storers/DiskSavedGameDataStorer.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Savers/EnabledSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Savers/DestructibleSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Dialogue/UnityUITimer.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedSimpleSpecialGridViewDemoScript.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarGridCircularAuto.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/BarkTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/ComponentUtility.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/SetQuestStateOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Text/StringFieldTextAreaAttribute.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Logic/DB/DialogueLua.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/TableConstructor.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/Dialogue/UnityDialogueUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/ElseifBlock.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewSimpleDemo.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Actor/Override/OverrideDialogueUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/Bark/UnityBarkUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/LuaOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Selector/Selector.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/BoolLiteral.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/LuaNetworkCommands.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Base/TweenHelper.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/KeepRectTransformOnscreen.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Base/DragEventHelperEx.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/GalleryVerticalItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Effects/StandardUIColorText.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Sequencer/SequencerCommandTimeline.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewDiagonalSelectDeleteDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Interaction/Selector.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Scripts/PersistentDataTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Scripts/TemplateCustomLua.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Twine/TwineStory.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Manager/ExtraDatabases.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Spawning/SpawnedObject.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Actor/Override/OverrideUIBase.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/StandardUIResponseButton.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/GridView/LoopGridViewItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/UnityUIQTEControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/GUIRoot.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/SaveSystem.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/BaseHorizontalItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/Legacy/AlertTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Selector/UsableUnityUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Text/GlobalTextTable.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaLibrary/StringLib.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/SafeConvert.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/StandardUIQTEControls.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/AnimationType.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/SequenceTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Spawning/SpawnedObjectManager.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewPullUpLoadMoreDemoScript.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Base/RotateScript.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/BaseHorizontalToggleItem.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/SpinView/SpinDateTimePickerDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/Statement.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaValue/LuaNumber.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Slider/SliderRangeTransition.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Message System/MessageSystemLogger.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarGridCircular.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Misc/CheckPhysics2D.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/EmphasisSetting.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Abstract/Dialogue/AbstractUIAlertControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Abstract/Dialogue/CanvasDialogueUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Options/Timeline/TimelineTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Misc/InstantiatePrefabs.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/RunLua/RunLuaTrack.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/LoadClickItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/Function.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/ScaledRect/ValueScale.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/TextMesh/LocalizeTextMesh.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Spawning/SpawnedObjectList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/PrimaryExpr.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarCircularMove.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/ConversationControl.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/SetComponentEnabledOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Celtx3/CeltxGem3ToDialogueDatabase.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/Legacy/ConversationStarter.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Tools/AutoSize.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Scripts/TemplateTextFieldUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/RunLua/RunLuaClip.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Triggers/Interaction/SelectorFollowTarget.cs" />
    <Compile Include="Assets/Plugins/DOTween/Modules/DOTweenModuleUnityVersion.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Effects/LocalizeUIText.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Message System/DataSynchronizer.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Quest/UnityUIQuestTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Savers/MultiActiveSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/LocalizedTextTable.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Notification/Notification.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaLibrary/BaseLib.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/Conversation/ConversationTrack.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/GUIProgressBar.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaLibrary/OSLib.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarLoop.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/GUIImage.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaValue/LuaUserdata.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/ScaledRect/ScaledValue.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/Schemas/Articy 2.2/XmlContentExport_FullProject_2_2.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Notification/NotificationContentFitterWithButton.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Selector/SelectorSimple.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/UILocalizationManager.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewSelectDeleteDemoScript.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowProgressBarLoopUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Dialogue/SMS/SMSDialogueUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Serializers/Binary/BinaryDataSerializer.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/ShowCursorWhileEnabled.cs" />
    <Compile Include="Assets/Plugins/FantaziaCharacterEditor/Scripts/GearsManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/AudioEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/GUIImageParams.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Utility/UnityUITypewriterEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/SequencerMessages.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Effects/UnityUIContinueButtonFastForward.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/GUIButton.cs" />
    <Compile Include="Assets/Plugins/DOTween/Modules/DOTweenModulePhysics2D.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/SetEnabledOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Demo/SimpleController.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Logic/DB/NLua_DialogueLua.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/Schemas/Articy 4.0/XmlContentExport_FullProject_4_0.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Slider/SliderCircular.cs" />
    <Compile Include="Assets/Plugins/DOTween/Modules/DOTweenModuleEPOOutline.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Misc/SaveSystemMethods.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/SimpleItemList.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListViewAnimation/ListViewExpandAnimationDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/SetLocalizedFont.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Save System/GameSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Quest/UnityUIQuestTrackTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Save System/PersistentDataManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/GroupExpr.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaValue/LuaMultiValue.cs" />
    <Compile Include="Assets/Plugins/DOTween/Modules/DOTweenModuleUI.cs" />
    <Compile Include="Assets/Plugins/FantaziaCharacterEditor/Scripts/GearButtonClicker.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Effects/StandardUIColorText.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelTreeViewSimple.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Attributes/VariablePopupAttribute.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Variable.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UnityEvents/CollisionEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandDelay.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/UnityBarkUIOnGUI.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/Chat/ChatViewDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Utility/CommonLibraryLua.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Serializers/JsonDataSerializer.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Quests/IncrementOnDestroy.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Message System/MessageEvents.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Common/TransitionHover.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Arcweave/ArcweaveProject.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/Access.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/BaseVerticalLineItem.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/NestedLeftRightItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/TextParserCommon.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/Access.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/UIInputField.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/UIScrollbarEnabler.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/AbstractTypewriterEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/StandardUIRoot.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/StopConversationIfTooFar.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Text/TextTable.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/TabView/TabViewGroup.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewBottomToTopDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/FunctionBody.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandZoom2D.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewMultiplePrefabDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Cinemachine/Sequencer Commands/SequencerCommandCinemachinePriority.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/GridView/GridItemGroup.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/Legacy/DialogueTriggerEvent.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewSimpleDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Quests/QuestLogWindowHotkey.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/SendMessageOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/StaggeredView/StaggeredViewSimpleTopToBottomDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/UITextField.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Interaction/ProximitySelector.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Savers/DestructibleSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/StandardDialogueUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Selector/UnityUISelectorDisplay.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelGridViewDelete.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowProgressBarUI.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/TextDescRowColItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Tools/Navigation.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewClickLoadMoreDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/NameAccess.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Quest/UnityUIQuestGroupTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/DialogueTime.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/StaggeredView/StaggeredViewTopToBottomDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/MVC/Actor/DefaultCameraAngle.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Manager/DialogueSystemController.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Message System/MessageSystemLogger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/WhileStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Quest/UnityUIQuestLogWindow.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Manager/AssetBundleManager.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewComplexDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/UIButtonKeyTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaValue/LuaNil.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Tooltip/TooltipInDetermine.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/TabView/TabViewSimple.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/SpinView/SpinTimePickerDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/Quest/QuestTracker.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Toggle/ToggleSwap.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/LoadItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/SetEventSystem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Savers/AnimatorSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/UnityDialogueUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/Quest/UnityGUIQuestLogWindow.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/TreeViewDataSourceMgr.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Gradient/GradientText.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/StaggeredGridView/LoopStaggeredGridViewItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/ArticyLuaFunctions.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Logic/Text/Emphasis.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/Field.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListViewAnimation/ListViewAddAnimationDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/StandardUISubtitlePanel.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowContentFitterUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/StartConversationOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelNested.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/DraggableVerticalItem.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewSelectDeleteDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Effects/StandardUIContinueButtonFastForward.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewDiagonalDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Save System/PersistentDestructible.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/QuestTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Triggers/Trigger/RangeTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/LocalizeUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/Bark/BarkClip.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Effects/GUIEffectTrigger.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarBubble.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/SequencerMessage/SequencerMessageClip.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Tools/UnityGUITools.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/View/Dialogue/IDialogueUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/NilLiteral.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarGridLinearAuto.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Controller/BarkController.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/UIVisibility.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Tools/TextStyle.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Notification/NotificationWithButtonUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/MVC/Actor/DialogueActor.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandWaitForMessage.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ResizableWindow/WindowResizeHandle.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/IStandardDialogueUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/StandardUIResponseMenuControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Serializers/Binary/QuaternionSerializationSurrogate.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedListViewLeftToRightDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Importers/Arcweave/ArcweaveLua.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/ExpandAnimationItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Storers/EncryptionUtility.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/UIShowHideController.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/View/Bark/IBarkUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/Dialogue/UnityTextFieldUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/DeselectPreviousOnPointerEnter.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/IconItem.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Common/EventForward.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Text/StringAsset.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaLibrary/FileLib.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewPullDownRefreshDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Effects/LocalizeUIText.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/PlaySequence/PlaySequenceBehaviour.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/ContentFitterItemData.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Quests/QuestGroupRecord.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/BaseRowColItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/UIButtonKeyTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/UIPanel.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Quest/StandardUIQuestTemplateAlternateDescriptions.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/Dialogue/Controls/UnityAlertControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/Conversation/ConversationMixerBehaviour.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Manager/Localization.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelNestedSimple.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/Dialogue/Controls/UnityUIRoot.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/UIDropdownField.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/GUIButton.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Dropdown/DropdownMultiCheckTransition.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewContentFitterDemoScript.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Base/FPSDisplay.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Effects/FadeEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Quest/UnityUIQuestTitle.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/GUIControl.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Dialogue/StandardUISubtitlePanel.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Quest/StandardUIQuestTitleButtonTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/ParamList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Manager/ActiveConversationRecord.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/FadeEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Sequencer.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/UIAnimationTransitions.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelMenu.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/QuestState/QuestStateTrack.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/StaggeredGridView/StaggeredGridItemGroup.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/BaseRowColItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/Function.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ResponsiveView/ResponsiveViewDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/KeyValue.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Effects/UnityUIContinueButtonFastForward.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/UnityUITextFieldUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/UnityUIDialogueControls.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/SliderItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandAnimatorFloat.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/SetActiveOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/QuestState/SetQuestStateBehaviour.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/Schemas/Articy 2.4/Articy_2_4_Tools.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandSwitchCamera.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/BarkOnIdle.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Serializers/Binary/BinaryDataSerializer.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandLoadLevel.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/NameValue.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/IconTextDescItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Savers/MultiEnabledSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Quests/QuestLog.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/TypewriterUtility.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/PopupMenu/PopupMenu.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaValue/LuaBoolean.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/QuestState/QuestStateMixerBehaviour.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Selector/StandardUsableUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Manager/PersistentDataSettings.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UnityEvents/TagMask.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBar.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/View/View/ConversationView.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelLoad.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/UnityUIDialogueUI.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowRadioButtonUI.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListViewAnimation/ListViewDeleteAnimationDemoScript.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/BaseHorizontalItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Demo/NavigateOnMouseClick.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/UnityUITypewriterEffect.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/PageViewItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/ItemValue.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Effects/UpdateLocalizedUITexts.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/Term.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewMultiplePrefabDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Effects/TimerEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Save System/LevelManager.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/BaseVerticalLineItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/Bark/UnityBarkUIOnGUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Chat Mapper/ChatMapperToDialogueDatabase.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/TreeViewItemCountMgr.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/SetActiveOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandAnimatorPlayWait.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowUI.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarBubbleAuto.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/SpecialGridView/SpecialGridViewSimpleTopToBottomDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Scripts/Editor/CustomFieldType_TemplateType.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Utility/LinkUtility.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Transitions/LoadingScreenProgressBar.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Interaction/SelectionDelegates.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Location.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/SetEventSystem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Aurora/NWNUtility.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/NestedSimpleItemData.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarPatternAuto.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/StringLiteral.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowCheckBox.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/View/Bark/BarkGroupManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/ShowCursorOnConversation.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Editor/Text/TextTableAssetMenu.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/GUIWindow.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelSpecial.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/ConversationOverrideDisplaySettings.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/AlertTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/FunctionValue.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/CharacterType.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/Legacy/BarkStarter.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/DialogueSystemSceneEvents.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/UITools.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Serializers/JsonDataSerializer.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UnityEvents/TagMaskEvent.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/SpinView/SpinDatePickerDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Celtx/CeltxToDialogueDatabase.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/ImageItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Scripts/Aurora/TemplateNWScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Quest/Common/StandardUIButtonTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/Expr.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Utility/UnityUIIgnorePauseCodes.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/Common/ClickEventListener.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/ItemData.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/DialogueSystemFields.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Misc/SaveSystemTestMenu.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Actor.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/ParserInput.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Effects/GUIEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/CursorControl.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/View/Dialogue/ITextFieldUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/ReturnStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/OperatorExpr.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Utility/CommonLibraryLua.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/NameAccess.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Arcweave/ArcweaveImporter.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/TextMesh/LocalizeTextMesh.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Manager/DialogueManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/ConversationLogger.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelGridViewLoad.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Message System/MessageEvents.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/DialogueDebug.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Triggers/Interaction/Usable.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/DialogueEntrySortMode.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/RunLua/RunLuaBehaviour.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/LODManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/Panel Numbers/PanelNumberUtility.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandLiveCamera.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/GUIImage.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/SetLocalizedFont.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/RuntimeTypeUtility.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/MVC/Model/DialogueDatabase.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/GridView/LoopGridView.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/VarName.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/StandardUITimer.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Manager/ExtraDatabases.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/StaggeredGridView/LoopStaggeredGridView.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/Dialogue/Controls/UnityResponseMenuControls.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/TabView/Tab.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/DeselectPreviousOnPointerEnter.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Chat Mapper/ChatMapperProject.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/Bark/BarkBehaviour.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/DialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Abstract/Dialogue/AbstractUIQTEControls.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/SliderComplexItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/FunctionCall.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Abstract/Dialogue/AbstractUIResponseMenuControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Wrapper/NLua/NLua_Lua.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Effects/AudioEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/ShowCursorOnConversation.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/LoadComplexItem.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowMultiButtonUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Wrapper/Lua Interpreter/Lua.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/CheckPhysics2D.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/QueuedUIAlert.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/BaseExpr.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/IconItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/EntrytagFormat.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/ScriptableObjectUtility.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Message System/MessageSystem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/SMS/SMSDialogueUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/StandardUIInputField.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowContentFitter.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Misc/EditorNote.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/View/Shared/DisplaySettings.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/ChatMsgDataSourceMgr.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Transitions/LoadingScreenProgressBar.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/BaseVerticalItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandCamera.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/StandardUIMenuPanel.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/DataSourceMgr.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Utility/LuaConsole.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Attributes/LuaScriptWizardAttribute.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Utility/ConditionPriorityUtility.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/EnableOnStart.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Quest/StandardUIQuestTracker.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/FunctionName.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Celtx/CeltxFields.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/View/Bark/BarkGroupMember.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/DoStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/UnityUISubtitleControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Abstract/Dialogue/AbstractUIControls.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/DeleteAnimationItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Triggers/Interaction/ProximitySelector.cs" />
    <Compile Include="Assets/Plugins/FantaziaCharacterEditor/Scripts/CharacterAnimator.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/ToggleUtility.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Quest/StandardUIQuestTitleButtonTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/UnityGUIQuestLogWindow.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Utility/LuaConsole.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Quest/Common/StandardUIFoldoutTemplate.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Dropdown/DropdownMultiCheck.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/NilLiteral.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/ExpandItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/ArticySchemaTools.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Quests/QuestStateDispatcher.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Demo/Scenes/New Input System/DemoInputRegistration.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Quest/Common/StandardUIButtonTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Transitions/SceneTransitionManager.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/IconTextDescItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Save System/PersistentDestructible.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Toast/ToastContentFitterUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Savers/EnabledSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/ForInStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Text/StringField.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/StartSequenceOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelTreeView.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Toast/Toast.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Scripts/SequencerCommandTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Save System/GameSaver.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/TabView/TabView.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/OperatorExpr.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/StartConversationOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Parser.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/DraggableView/DraggableViewFadeTopToBottomDemoScript.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarAuto.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/UnityUIBarkSubtitleDialogueUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/Dialogue/Controls/UnitySubtitleControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/UnityUIScrollbarEnabler.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ContextMenu/ContextMenuLongPress.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/UnityUIAlertControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Misc/DontDestroyGameObject.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/Expr.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/GUIControl.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Utility/ConversationControl.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/StaggeredView/StaggeredViewSimpleLeftToRightDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/MethodCall.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/Legacy/SequenceStarter.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/Chunk.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Text/EncodingType.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Abstract/Dialogue/AbstractDialogueUI.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelDelete.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/SymbolExtensions.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/Chat/ChatViewChangeViewportHeightScript.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/ContentFitterItem.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/IconTextItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Templates/SaverTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Attributes/LuaConditionsWizardAttribute.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Quest/UnityUIQuestTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/OverrideUnityUIDialogueControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Logic/Model/ConversationModel.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Triggers/Trigger/ConditionObserver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Save System/PersistentActiveDataMultiple.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaValue/LuaValue.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/PrimaryExpr.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowProgressBar.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/ListExtensions.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Celtx3/CeltxFields.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/FunctionCall.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UnityEvents/DisappearEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Bark/UnityUIBarkUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/SetComponentEnabledOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Toggle/ToggleTextSimple.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Yarn2/YarnEnumDomain.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/PopupMenu/PopupMenuLongPress.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Dialogue/UnityUITextFieldUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Spawning/SpawnedObjectManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Utility/UnityUIScrollbarEnabler.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Cinemachine/Triggers/CinemachineCameraPriorityOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/Legacy/QuestTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/GUIWindow.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Attributes/DialogueTriggerEventAttribute.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/HelpBoxAttribute.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Savers/PositionSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Quest/UnityUIQuestTracker.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarCircularRound.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/MVC/View/TextlessBarkUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Scripts/TemplateQuestLogWindow.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Effects/StandardUIIgnorePauseCodes.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/PopupMenu/PopupMenuLeftClick.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Manager/DialogueSystemController.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/InputField/InputFieldSimple.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UnityEvents/ParameterEvents.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/ListView/LoopListView2.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Interaction/SelectorFollowTarget.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Conversation.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Demo/SmoothCameraWithBumper.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Articy/Schemas/Articy 4.0/Articy_4_0_Tools.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/MVC/Actor/Override/OverrideDialogueUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/MVC/Actor/Override/OverrideDisplaySettings.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Toggle/ToggleTransition.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandFade.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Toggle.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Tooltip/TooltipSpecial.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Bark/StandardBarkUI.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/TreeView/TreeViewWithChildIndentDemo.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Triggers/TimelineTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/View/Shared/InputTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Triggers/Trigger/BarkOnIdle.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Storers/SavedGameDataStorer.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Message System/IMessageHandler.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/DataSource/SimpleItemData.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/GameObjectUtility.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/FieldType.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UnityEvents/CollisionEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Misc/ScenePortal.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/LuaValue/LuaMethodFunction.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/MorePhysics2D.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Base/OneDirectionDragHelper.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/NumberLiteral.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Toggle/ToggleCheck.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Spawning/SpawnedObjectList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/BarkOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Quest/Common/StandardUIFoldoutTemplate.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelGallery.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandAudioWaitOnce.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Serializers/Binary/Vector3SerializationSurrogate.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Common/TransitionTwo.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/SaveSystem.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/TreeView/TreeViewSimpleDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/GUIVisibleControl.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/View/Dialogue/ResponseButtonAlignment.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ResponsiveView/ResponsiveViewRefreshLoadDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Effects/StandardUIIgnorePauseCodes.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Button/ButtonTransition.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/OnEvent/StartSequenceOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/GUILabel.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Save System/LevelManager.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/GUIScrollView.cs" />
    <Compile Include="Assets/Plugins/DOTween/Modules/DOTweenModuleSprite.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/SequencerShortcuts.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Utility/InputModuleSwitch.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Message System/DataSynchronizer.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/WhileStmt.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ResizableWindow/WindowDrag.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/GalleryHorizontalItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Demo Scripts/DemoMenu.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/StringLiteral.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/View/Bark/TextlessBarkUI.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/Bark/BarkMixerBehaviour.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Dialogue/Portraits/UseAnimatedPortraits.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Switch/Switch.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Dialogue/Panel Numbers/MenuPanelNumber.cs" />
    <Compile Include="Assets/Plugins/PluginsResourcesLoad.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/UIButtonKeyTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Attributes/ConversationPopupAttribute.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ContextMenu/ContextMenuRightClick.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/ImageItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Quests/IncrementOnDestroy.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Legacy GUI/ContinueButtonFastForward.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandTextInput.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Utility/TextMeshProTypewriterEffect.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ResizableWindow/WindowResize.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/DontDestroyGameObject.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/RangeTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Utility/DatabaseUtility.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/UIPanel.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Demo Scripts/NavigateOnMouseClick.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Notification/NotificationWithButton.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/ExprStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/InstantiatePrefabs.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Utility/ConversationPositionStack.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Abstract/Selector/AbstractUsableUI.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/Common/ItemPosMgr.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Logic/Shared/CharacterInfo.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Base/AutoSetAnchorPosForIphonex.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Data/Link.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Savers/ActiveSaver.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/MVC/View/BarkDialogueUI.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/TextDescRowColItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/Triggers/SetQuestStateOnDialogueEvent.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/Dialogue/Controls/UnityDialogueUIControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Logic/Shared/ConversationState.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewSimpleFilterDemoScript.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewFilterDemoScript.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/SpinPickerItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/Save System/Storers/PlayerPrefsSavedGameDataStorer.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Selector/SelectorUseStandardUIElements.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandQTE.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Quests/QuestStateListener.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/GUI Controls/Effects/SlideEffect.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/LocalFunc.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/DraggableView/DraggableViewFadeLeftToRightDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/Dialogue/Portraits/UseAnimatedPortraits.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/InputFieldItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Extra/LuaInterpreterExtensions.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Abstract/Dialogue/AbstractUIRoot.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ProgressBar/ProgressBarGridLinear.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandMoveTo.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ModalWindow/ModalWindowInputFieldUI.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/NestedGridViewLeftRightItem.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/GridView/GridViewSimpleDiagonalDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Quest/Common/StandardUIInstancedContentManager.cs" />
    <Compile Include="Assets/Plugins/DOTween/Modules/DOTweenModulePhysics.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Demo/Scenes/New Input System/DemoInputControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Quest/Common/StandardUITextTemplate.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/ToggleRowColItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Dialogue/UnityUIBarkSubtitleDialogueUI.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/Switch/SwitchSimple.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/TreeViewSimpleItem.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/LocalVar.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/BoolLiteral.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Chunk/BreakStmt.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelTreeViewSticky.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Utility/UnityUIColorText.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Unity UI/Quest/UnityUIQuestLogWindow.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Model/Logic/Text/FormattedText.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/CoroutineUtility.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Misc/Dimension.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/IfStmt.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Savers/PositionSaver.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Scripts/StaggeredGridView/StaggeredGridItemPool.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Wrappers/UI/UIScrollbarEnabler.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ButtonPanel/ButtonPanelStaggeredView.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/MVC/Sequencer/Commands/SequencerCommandVoice.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Standard/Quest/StandardUIQuestLogWindow.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewPullDownRefreshDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/Legacy/DialogueEventStarter.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Message System/MessageArgs.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/PageView/PageViewDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Parser/Syntax/NumberLiteral.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/PopupMenu/PopupMenuRightClick.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Abstract/Dialogue/AbstractDialogueUIControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/FunctionBody.cs" />
    <Compile Include="Assets/Plugins/CleanFlatUI/Scripts/ContextMenu/ContextMenuLeftClick.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/Expr/VariableArg.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Utility/Attributes/QuestPopupAttribute.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Importers/Yarn2/YarnCustomCommands.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Misc/ScenePortal.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Triggers/DialogueSystemTrigger.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/Dialogue/Controls/UnityDialogueControls.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Standard/Quest/Common/StandardUIContainerTemplate.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/UI/IEventSystemUser.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/NestedView/NestedListViewTopToBottomDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Triggers/Interaction/Usable.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewPullDownRefreshOrPullUpLoadDemo.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/Item/IconTextItemList.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Common/Scripts/Save System/Misc/SavedGameData.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/UI/Utility/UIButtonKeyTrigger.cs" />
    <Compile Include="Assets/Plugins/SuperScrollView/Demo/Scripts/ViewDemo/ListView/ListViewMultiplePrefabLeftToRightDemoScript.cs" />
    <Compile Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Options/Timeline/Playables/ShowAlert/AlertTrack.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Prefabs/Art/Fonts/Orbitron/SIL Open Font License.txt" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMPro.cginc" />
    <None Include="Assets/Plugins/Spine/version.txt" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Unity UI/_README.txt" />
    <None Include="Assets/Plugins/TopDownEngine/ThirdParty/MoreMountains/MMInterface/Common/Shaders/GreyScale.shader" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMP_Bitmap.shader" />
    <None Include="Assets/Plugins/TopDownEngine/ThirdParty/MoreMountains/MMInterface/Common/Shaders/VerticalGradient.shader" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Prefabs/Camera Angle Studio/_README.txt" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Prefabs/Art/Textures/Circle/_README.txt" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Prefabs/Art/Textures/UnityUI/_README.txt" />
    <None Include="Assets/Plugins/TopDownEngine/license.txt" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMPro_Mobile.cginc" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMP_SDF SSD.shader" />
    <None Include="Assets/Plugins/Pixel Crushers/Common/_README.txt" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Demo/Scenes/New Input System/_New_Input_System_Setup.txt" />
    <None Include="Assets/Plugins/DOTween/DOTween.dll" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Demo/_README.txt" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets/Plugins/TopDownEngine/ThirdParty/MoreMountains/InventoryEngine/readme.txt" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Link/link_asmdef.xml" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Prefabs/Art/Textures/Shared Textures/_README.txt" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Link/_README.txt" />
    <None Include="Assets/Plugins/DOTween/DOTween.XML" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Prefabs/Art/Shared Audio/_README.txt" />
    <None Include="Assets/Plugins/TopDownEngine/ThirdParty/MoreMountains/MMInterface/Styles/Mini/Fonts/Lato/OFL.txt" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Shaders/LinesColoredBlended.shader" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMP_SDF-Surface.shader" />
    <None Include="Assets/Plugins/TextMesh Pro/Sprites/EmojiOne Attribution.txt" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMP_SDF.shader" />
    <None Include="Assets/Plugins/Plugins.asmdef" />
    <None Include="Assets/Plugins/TopDownEngine/readme.txt" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMP_SDF-Mobile SSD.shader" />
    <None Include="Assets/Plugins/TextMesh Pro/Resources/LineBreaking Leading Characters.txt" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Prefabs/Art/Fonts/Bitwise Font/!license.txt" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMPro_Properties.cginc" />
    <None Include="Assets/Plugins/DOTween/Editor/DOTweenEditor.XML" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/UI/Legacy/_README.txt" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMPro_Surface.cginc" />
    <None Include="Assets/Plugins/FantaziaCharacterEditor/readme.txt" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Link/link.xml" />
    <None Include="Assets/Plugins/FantaziaCharacterEditor/SpineSkeletonData/Character.atlas.txt" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/_README.txt" />
    <None Include="Assets/Plugins/TextMesh Pro/Resources/LineBreaking Following Characters.txt" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Demo/Art/Sounds/_README.txt" />
    <None Include="Assets/Plugins/DOTween/Editor/DOTweenEditor.dll" />
    <None Include="Assets/Plugins/TopDownEngine/ThirdParty/MoreMountains/InventoryEngine/license.txt" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/_README.txt" />
    <None Include="Assets/Plugins/TopDownEngine/third-party-notices.txt" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Third Party Support/_README.txt" />
    <None Include="Assets/Plugins/TopDownEngine/ThirdParty/MoreMountains/MMInterface/Common/Shaders/HorizontalGradient.shader" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Scripts/Lua/Lua Interpreter/_README.txt" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMP_SDF Overlay.shader" />
    <None Include="Assets/Plugins/SuperScrollView/Scripts/Version 2.5.3.txt" />
    <None Include="Assets/Plugins/FantaziaCharacterEditor/SpineProjectFile/Character.atlas.txt" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMP_Bitmap-Mobile.shader" />
    <None Include="Assets/Plugins/TextMesh Pro/Fonts/LiberationSans - OFL.txt" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Templates/Scripts/_README.txt" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMP_SDF-Mobile.shader" />
    <None Include="Assets/Plugins/DOTween/readme.txt" />
    <None Include="Assets/Plugins/TextMesh Pro/Shaders/TMP_Sprite.shader" />
    <None Include="Assets/Plugins/Pixel Crushers/Dialogue System/Wrappers/Deprecated/_README.txt" />
    <None Include="Assets/Plugins/TopDownEngine/IMPORTANT-HOW-TO-INSTALL.txt" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ProfilerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.OSXStandalone.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.Protobuf">
      <HintPath>Assets/AssetRaw/DLL/Google.Protobuf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Enrichers.WithCaller">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Enrichers.WithCaller.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CodeAnalysis">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Editor/Analysis/DLLs/Microsoft.CodeAnalysis.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Satori">
      <HintPath>Assets/Plugins/Nakama/Runtime/Plugins/Satori.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog">
      <HintPath>Assets/AssetRaw/DLL/Serilog.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.System.Text.Encodings.Web">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.System.Text.Encodings.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource">
      <HintPath>Assets/AssetRaw/DLL/System.Diagnostics.DiagnosticSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library/PackageCache/com.unity.collections@2.5.1/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="YarnSpinner.Compiler">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/YarnSpinner.Compiler.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library/PackageCache/com.unity.ext.nunit@2.0.3/net40/unity-custom/nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.NET.StringTools">
      <HintPath>Assets/AssetRaw/DLL/Microsoft.NET.StringTools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ReportGeneratorMerged">
      <HintPath>Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets/TEngine/Libraries/System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Sinks.Console">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Sinks.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Immutable">
      <HintPath>Assets/AssetRaw/DLL/System.Collections.Immutable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MessagePack">
      <HintPath>Assets/AssetRaw/DLL/MessagePack.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MessagePack.Annotations">
      <HintPath>Assets/AssetRaw/DLL/MessagePack.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Metadata">
      <HintPath>Assets/AssetRaw/DLL/System.Reflection.Metadata.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets/Plugins/DOTween/DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nakama">
      <HintPath>Assets/Plugins/Nakama/Runtime/Plugins/Nakama.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Ben.Demystifier">
      <HintPath>Assets/AssetRaw/DLL/Ben.Demystifier.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="YarnSpinner">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/YarnSpinner.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Sinks.Debug">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Sinks.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Assets/AssetRaw/DLL/Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="dnlib">
      <HintPath>Library/PackageCache/com.code-philosophy.hybridclr@9429a4d24d/Plugins/dnlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LZ4">
      <HintPath>Library/PackageCache/com.code-philosophy.hybridclr@9429a4d24d/Plugins/LZ4.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>Assets/Plugins/DOTween/Editor/DOTweenEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS_I2Loc.Xcode">
      <HintPath>Assets/TEngine/Editor/Localization/Unity XCode/UnityEditor.iOS_I2Loc.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1/Runtime/Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library/PackageCache/com.unity.nuget.mono-cecil@1.11.4/Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.System.Threading.Tasks.Extensions">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.Microsoft.Extensions.FileSystemGlobbing">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.Microsoft.Extensions.FileSystemGlobbing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Channels">
      <HintPath>Assets/AssetRaw/DLL/System.Threading.Channels.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.System.Text.Json">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.System.Text.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CodeAnalysis.CSharp">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Editor/Analysis/DLLs/Microsoft.CodeAnalysis.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Sinks.File">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Sinks.File.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Unity.TextMeshPro.csproj" />
    <ProjectReference Include="Unity.TextMeshPro.Editor.csproj" />
    <ProjectReference Include="Unity.InputSystem.csproj" />
    <ProjectReference Include="spine-unity.csproj" />
    <ProjectReference Include="TEngine.Runtime.csproj" />
    <ProjectReference Include="UniTask.csproj" />
    <ProjectReference Include="UnityEditor.UI.csproj" />
    <ProjectReference Include="UnityEngine.UI.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
