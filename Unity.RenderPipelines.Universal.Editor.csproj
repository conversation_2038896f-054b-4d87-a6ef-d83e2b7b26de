﻿<Project ToolsVersion="Current">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp/obj/$(Configuration)/$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>9.0</LangVersion>
    <Configurations>Debug;Release</Configurations>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Unity.RenderPipelines.Universal.Editor</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp/bin/Debug/</OutputPath>
    <DefineConstants>UNITY_2022_3_52;UNITY_2022_3;UNITY_2022;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION;ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT;ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE;PLATFORM_IOS;TEXTCORE_1_0_OR_NEWER;ENABLE_RUNTIME_GI;ENABLE_GAMECENTER;ENABLE_NETWORK;ENABLE_IOS_ON_DEMAND_RESOURCES;ENABLE_IOS_APP_SLICING;PLAYERCONNECTION_LISTENS_FIXED_PORT;DEBUGGER_LISTENS_FIXED_PORT;PLATFORM_SUPPORTS_ADS_ID;SUPPORT_ENVIRONMENT_VARIABLES;PLATFORM_SUPPORTS_PROFILER;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_ETC_COMPRESSION;UNITY_IPHONE_API;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;UNITY_IOS;PLATFORM_IPHONE;UNITY_IPHONE;UNITY_HAS_GOOGLEVR;ENABLE_SPATIALTRACKING;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_OSX;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;DOTWEEN;MOREMOUNTAINS_INTERFACE;MOREMOUNTAINS_TOPDOWNENGINE;MOREMOUNTAINS_INVENTORYENGINE;COZY_WEATHER;COZY_3_AND_UP;COZY_URP;TextMeshPro;ENABLE_LOG;ENABLE_HYBRIDCLR;ENABLE_XR_MODULE;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER;UNITY_EDITOR_ONLY_COMPILATION</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp/bin/Release/</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>True</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.22</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Editor:5</UnityProjectType>
    <UnityBuildTarget>iOS:9</UnityBuildTarget>
    <UnityVersion>2022.3.52f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="/Users/<USER>/.vscode/extensions/visualstudiotoolsforunity.vstuc-1.1.2/Analyzers/Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="/Users/<USER>/Documents/Unity/PokemonUnity/Pets_SanGuo/Library/PackageCache/dev.yarnspinner.unity@be66087b21/SourceGenerator/YarnSpinner.Unity.SourceCodeGenerator.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AssetPostProcessors/ModelPostProcessor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShaderGraph/Targets/UniversalSpriteCustomLitSubTarget.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/UniversalAdditionalLightDataEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/GUIFramework/GUIState.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/Converter/UpgradeURP2DAssetsContainer.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/Selection/ISelector.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Camera/UniversalRenderPipelineCameraUI.PhysicalCamera.Drawers.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/VFXURPLitMeshOutput.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/ConverterItemInfo.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Decal/DisplacableRectHandles.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditablePath/ISnapping.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/Converter/ParametricToFreeformLightUpgrader.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Targets/UniversalTarget.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Overrides/DepthOfFieldEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Decal/DecalShaderGraphGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/Selection/IndexedSelection.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/UniversalRenderPipelineAsset/UniversalRenderPipelineAssetUI.Drawers.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/Shapes/Spline.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AssemblyInfo.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/Shaders/BakedLitShader.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/ParticleGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/UniversalRenderPipelineLightEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/UniversalRendererDataEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditablePath/Snapping.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/Shaders/SimpleLitShader.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/Shaders/ParticlesUnlitShader.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderStripTool.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Camera/UniversalRenderPipelineCameraUI.Skin.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/PostProcessDataEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/ShadingModels/LitDetailGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/Renderer2DAnalytics.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/ConversionIndexers.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/RunItemContext.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/View/IDrawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/BuiltInToURPConverterContainer.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Decal/DecalProjectorEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/Selection/SerializableSelection.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/GUIFramework/GenericControl.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/RenderPipelineConverterContainer.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AssetPostProcessors/SketchupMaterialDescriptionPostprocessor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Targets/UniversalLitSubTarget.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Overrides/MotionBlurEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/GlobalSettings/UniversalGlobalSettingsEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/UniversalRenderPipelineAsset/SerializedUniversalRenderPipelineAsset.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/VFXAbstractParticleURPLitOutput.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditorTool/PathComponentEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Decal/CreateDecalShaderGraph.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/SortingLayerDropDown.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/Renderer2DMenus.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShadowCaster2DShapeTool.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AssetPostProcessors/FBXMaterialDescriptionPreprocessor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/Shaders/UnlitShader.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditorTool/PathEditorTool.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditablePath/EditablePath.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShaderGraph/Targets/SpriteSubTargetUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditorTool/PathEditorToolExtensions.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/InitializeConverterContext.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/ShaderGraphLitGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/CompositeShadowCaster2DEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/UniversalSpeedTree8MaterialUpgrader.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/Converter/URP2DConverterUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/GUIFramework/GUISystem.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditablePath/ControlPoint.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/GUIFramework/DefaultControl.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/GlobalSettings/SerializedUniversalRenderPipelineGlobalSettings.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditorTool/ScriptableData.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/ConverterItemDescriptor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/UniversalRenderPipelineAsset/UniversalRenderPipelineAssetUI.Skin.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/UniversalStructFields.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Lighting/UniversalRenderPipelineLightUI.Drawers.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Targets/UniversalDecalSubTarget.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Overrides/ColorLookupEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Camera/UniversalRenderPipelineCameraUI.Rendering.Skin.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/GUIFramework/SliderAction.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditablePath/IEditablePath.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/VFXURPLitPlanarPrimitiveOutput.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/Selection/ISelectable.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Overrides/ChannelMixerEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/ShadingModels/BakedLitGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/Shapes/Polygon.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Targets/UniversalUnlitSubTarget.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/RendererFeatures/ScreenSpaceShadowsEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditablePath/EditablePathUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/DefaultScene/UniversalProjectSettings.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/LightBatchingDebugger/LightBatchingDebugger.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/RendererFeatures/FullScreenPassRendererFeatureEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/GUIFramework/IGUIState.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Lighting/UniversalRenderPipelineLightUI.Skin.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShaderGraph/Targets/UniversalSpriteUnlitSubTarget.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AssetPostProcessors/FBXArnoldSurfaceMaterialDescriptionPreprocessor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/AssetCallbacks/CreateUnlitShaderGraph.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Camera/UniversalRenderPipelineCameraUI.Environment.Drawers.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/Light2DEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Camera/UniversalRenderPipelineSerializedCamera.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/AnimationClipConverter.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Lighting/UniversalRenderPipelineLightUI.PresetInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/TerrainLitShaderGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Camera/UniversalRenderPipelineCameraUI.Output.Skin.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Targets/UniversalFullscreenSubTarget.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/RendererFeatures/ScreenSpaceAmbientOcclusionEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/View/Drawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/Converter/BuiltInToURP2DConverterContainer.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/View/IEditablePathView.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/GlobalSettings/UniversalRenderPipelineGlobalSettingsUI.Skin.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/RendererFeatures/RenderObjectsPassFeatureEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/GlobalSettings/UniversalRenderPipelineGlobalSettingsUI.Drawers.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/TrackballUIDrawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/CinemachineUniversalPixelPerfectEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ScriptableRendererDataEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/UniversalRenderPipelineAssetEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Camera/UniversalRenderPipelineCameraUI.Rendering.Drawers.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/Renderer2DDataEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditorTool/GenericScriptablePathInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/UpgradeUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderBuildPreprocessor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/ShadingModels/SimpleLitGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditorTool/ScriptablePathInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ScriptableRendererFeatureProvider.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AssetPostProcessors/MaterialPostprocessor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/Selection/ISelection.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/UniversalProperties.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/RenderPipelineConvertersEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AssetPostProcessors/PhysicalMaterial3DsMaxPreprocessor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditablePath/EditablePathExtensions.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Decal/CreateDecalProjector.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/AssetCallbacks/CreateLitShaderGraph.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/GUIFramework/HoveredControlAction.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/VFXURPBinder.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AssetPostProcessors/ShaderGraphMaterialsUpdater.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Targets/UniversalSubTarget.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/LightExplorer.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/Shapes/ShapeExtensions.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Analytics/AssetReimporterAnalytic.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Lighting/UniversalRenderPipelineSerializedLight.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/DecalMeshBiasTypeEnum.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/UniversalAnalytics.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AssetPostProcessors/ThreeDSMaterialDescriptionPostprocessor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/UniversalStructs.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditablePath/IEditablePathController.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditablePath/IUndoObject.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditorTool/GenericScriptablePath.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Camera/UniversalRenderPipelineCameraUI.Environment.Skin.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ScriptableRendererFeatureEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/GUIFramework/GenericDefaultControl.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Overrides/ColorCurvesEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderScriptableStripper.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/View/CreatePointAction.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/VFXURPLitQuadStripOutput.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/VFXShaderGraphGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/UniversalBlockFields.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/Shaders/ParticlesSimpleLitShader.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/ShaderGraphUnlitGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditablePath/MultipleEditablePathController.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShaderGraph/Targets/UniversalSpriteLitSubTarget.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Overrides/FilmGrainEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Camera/UniversalRenderPipelineCameraUI.Drawers.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/VFXURPSubOutput.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShadowCaster2DEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/GameObjectCreation.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/UniversalAdditionalCameraDataEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/RendererFeatures/DecalRendererFeatureEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/RendererFeatures/NewRendererFeatureDropdownItem.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/GlobalSettings/UniversalRenderPipelineGlobalSettingsProvider.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditablePath/BezierUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AnimationClipUpgrader_Types.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/Shaders/LitShader.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Decal/DecalProjectorEditor.Skin.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/UniversalRenderPipelineMaterialUpgrader.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/GUIFramework/GUIAction.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Overrides/LiftGammaGainEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Camera/UniversalRenderPipelineCameraEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/GUIFramework/Control.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AssetPostProcessors/UniversalRenderPipelineGlobalSettingsPostprocessor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/AssetCallbacks/CreateFullscreenShaderGraph.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/Selection/RectSelector.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/EditorUtils.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/PixelPerfectCameraEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/Selection/PointRectSelector.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/MaterialReferenceBuilder.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/GlobalSettings/UniversalGlobalSettingsCreator.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Overrides/ShadowsMidtonesHighlightsEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/Converters/RenderSettingsConverter.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/Shapes/IShape.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/Light2DEditorUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AnimationClipUpgrader.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/UniversalFields.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/GUIFramework/LayoutData.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Camera/UniversalRenderPipelineCameraUI.Output.Drawers.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Decal/ProjectedTransform.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/Converter/BuiltInToURP2DMaterialUpgrader.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/GUIFramework/CommandAction.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AssetVersion.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/AssetPostProcessors/AutodeskInteractiveMaterialImport.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Camera/UniversalRenderPipelineCameraUI.PresetInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/View/EditablePathView.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/RenderStateDataEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ContextualMenuDispatcher.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/ReadonlyMaterialConverter.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/Shaders/ParticlesLitShader.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/FreeformPathPresets.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/RenderPipelineConverter.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/ShadingModels/LitGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShaderGraph/AssetCallbacks/CreateSpriteUnlitShaderGraph.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditorTool/ScriptablePath.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderUtils.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShaderGraph/Nodes/LightTextureNode.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/Converters.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Overrides/TonemappingEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/EditablePath/EditablePathController.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Nodes/UniversalSampleBufferNode.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/UniversalMetadata.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/UpgradeCommon.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/SavedParameter.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShaderGraph/AssetCallbacks/CreateSpriteLitShaderGraph.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Overrides/BloomEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShapeEditor/GUIFramework/ClickAction.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGUI/BaseShaderGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Deprecated.cs" />
    <Compile Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShaderGraph/AssetCallbacks/CreateSpriteCustomLitShaderGraph.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/Varyings.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticlePlanarPrimitives/PassForward2D.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticlePoints/PassForward2D.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/converter_widget_item.uxml" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Templates/ShaderPass.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticlePlanarPrimitivesLit/PassSelection.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/converter_widget_main.uss" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/RendererFeatures/NewRendererFeature.cs.txt" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticleMeshesLit/PassSelection.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/VFXLit.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/VFXParticleMeshes.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticleLinesSW/PassForward2D.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/converter_editor.uss" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/ShaderPassDecal.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticleMeshesLit/PassDepth.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/UnlitGBufferPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticleLines/PassForward2D.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticlePlanarPrimitivesLit/PassDepthNormal.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/VFXParticleLinesSW.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/VFXParticleBasicCube.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/DecalMeshBiasTypeEnum.cs.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/LightBatchingDebugger/LayerBatch.uxml" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticleMeshesLit/PassDepthNormal.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Overrides/Shaders/ShadowsMidtonesHighlightsCurves.shader" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticleMeshesLit/PassDepthOrMV.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Unity.RenderPipelines.Universal.Editor.asmdef" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticlePlanarPrimitivesLit/PassDepthOrMV.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/PBRGBufferPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/VFXParticleCube.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticleMeshesLit/PassGBuffer.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/VFXLitVaryings.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/UnlitPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShaderGraph/Includes/SpriteUnlitPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/PBRForwardPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticlePlanarPrimitivesLit/PassDepth.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/VFXParticleLitPlanarPrimitive.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/VFXParticlePoints.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticleMeshesLit/Pass.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Overrides/Shaders/TrackballEditor.shader" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/converter_editor.uxml" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticleMeshesLit/PassForward.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/converter_widget.uxml" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/VFXParticleDecal.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Decal/DecalPass.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShaderGraph/Includes/SpriteNormalPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShaderGraph/Includes/SpriteForwardPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/ShaderVariablesDecal.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/ShaderPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/ShadowCasterPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/converter_widget_main.uxml" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/LightingMetaPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/VFXParticleLinesHW.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/ShaderGraph/Includes/SpriteLitPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/PBR2DPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/VFXSGSurfaceData.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticleMeshes/PassForward2D.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Overrides/Shaders/CurveBackground.shader" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticleHexahedron/PassForward2D.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/VFXPasses.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/SelectionPickingPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticlePlanarPrimitivesLit/PassShadowCaster.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Templates/SharedCode.template.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/LightBatchingDebugger/LightBatchingDebugger.uss" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/VFXParticleLitMesh.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticleMeshesLit/PassShadowCaster.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/DepthNormalsOnlyPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticlePlanarPrimitivesLit/PassGBuffer.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/ParticlePlanarPrimitivesLit/PassForward.template" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/2D/LightBatchingDebugger/LightBatchingDebugger.uxml" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/ShaderGraph/Includes/DepthOnlyPass.hlsl" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/Converter/converter_widget.uss" />
    <None Include="Library/PackageCache/com.unity.render-pipelines.universal@14.0.11/Editor/VFXGraph/Shaders/Templates/VFXParticlePlanarPrimitive.template" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.NVIDIAModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ProfilerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.OSXStandalone.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.Protobuf">
      <HintPath>Assets/AssetRaw/DLL/Google.Protobuf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Enrichers.WithCaller">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Enrichers.WithCaller.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CodeAnalysis">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Editor/Analysis/DLLs/Microsoft.CodeAnalysis.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Satori">
      <HintPath>Assets/Plugins/Nakama/Runtime/Plugins/Satori.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog">
      <HintPath>Assets/AssetRaw/DLL/Serilog.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.System.Text.Encodings.Web">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.System.Text.Encodings.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource">
      <HintPath>Assets/AssetRaw/DLL/System.Diagnostics.DiagnosticSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library/PackageCache/com.unity.collections@2.5.1/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="YarnSpinner.Compiler">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/YarnSpinner.Compiler.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library/PackageCache/com.unity.ext.nunit@2.0.3/net40/unity-custom/nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.NET.StringTools">
      <HintPath>Assets/AssetRaw/DLL/Microsoft.NET.StringTools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ReportGeneratorMerged">
      <HintPath>Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets/TEngine/Libraries/System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Sinks.Console">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Sinks.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Immutable">
      <HintPath>Assets/AssetRaw/DLL/System.Collections.Immutable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MessagePack">
      <HintPath>Assets/AssetRaw/DLL/MessagePack.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MessagePack.Annotations">
      <HintPath>Assets/AssetRaw/DLL/MessagePack.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Metadata">
      <HintPath>Assets/AssetRaw/DLL/System.Reflection.Metadata.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets/Plugins/DOTween/DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nakama">
      <HintPath>Assets/Plugins/Nakama/Runtime/Plugins/Nakama.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Ben.Demystifier">
      <HintPath>Assets/AssetRaw/DLL/Ben.Demystifier.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="YarnSpinner">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/YarnSpinner.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Sinks.Debug">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Sinks.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Assets/AssetRaw/DLL/Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="dnlib">
      <HintPath>Library/PackageCache/com.code-philosophy.hybridclr@9429a4d24d/Plugins/dnlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LZ4">
      <HintPath>Library/PackageCache/com.code-philosophy.hybridclr@9429a4d24d/Plugins/LZ4.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>Assets/Plugins/DOTween/Editor/DOTweenEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS_I2Loc.Xcode">
      <HintPath>Assets/TEngine/Editor/Localization/Unity XCode/UnityEditor.iOS_I2Loc.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1/Runtime/Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library/PackageCache/com.unity.nuget.mono-cecil@1.11.4/Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.System.Threading.Tasks.Extensions">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.Microsoft.Extensions.FileSystemGlobbing">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.Microsoft.Extensions.FileSystemGlobbing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Channels">
      <HintPath>Assets/AssetRaw/DLL/System.Threading.Channels.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.System.Text.Json">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.System.Text.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CodeAnalysis.CSharp">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Editor/Analysis/DLLs/Microsoft.CodeAnalysis.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Sinks.File">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Sinks.File.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Unity.RenderPipelines.Universal.Runtime.csproj" />
    <ProjectReference Include="Unity.Postprocessing.Runtime.csproj" />
    <ProjectReference Include="Unity.Postprocessing.Editor.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Runtime.csproj" />
    <ProjectReference Include="Unity.RenderPipelines.Core.Editor.csproj" />
    <ProjectReference Include="Unity.ShaderGraph.Editor.csproj" />
    <ProjectReference Include="Unity.Burst.Editor.csproj" />
    <ProjectReference Include="Unity.Mathematics.Editor.csproj" />
    <ProjectReference Include="UnityEditor.UI.csproj" />
    <ProjectReference Include="UnityEngine.UI.csproj" />
    <ProjectReference Include="UnityEditor.TestRunner.csproj" />
    <ProjectReference Include="UnityEngine.TestRunner.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
