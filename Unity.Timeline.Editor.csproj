﻿<Project ToolsVersion="Current">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp/obj/$(Configuration)/$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>9.0</LangVersion>
    <Configurations>Debug;Release</Configurations>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Unity.Timeline.Editor</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp/bin/Debug/</OutputPath>
    <DefineConstants>UNITY_2022_3_52;UNITY_2022_3;UNITY_2022;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION;ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT;ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE;PLATFORM_IOS;TEXTCORE_1_0_OR_NEWER;ENABLE_RUNTIME_GI;ENABLE_GAMECENTER;ENABLE_NETWORK;ENABLE_IOS_ON_DEMAND_RESOURCES;ENABLE_IOS_APP_SLICING;PLAYERCONNECTION_LISTENS_FIXED_PORT;DEBUGGER_LISTENS_FIXED_PORT;PLATFORM_SUPPORTS_ADS_ID;SUPPORT_ENVIRONMENT_VARIABLES;PLATFORM_SUPPORTS_PROFILER;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_ETC_COMPRESSION;UNITY_IPHONE_API;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;UNITY_IOS;PLATFORM_IPHONE;UNITY_IPHONE;UNITY_HAS_GOOGLEVR;ENABLE_SPATIALTRACKING;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_OSX;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;DOTWEEN;MOREMOUNTAINS_INTERFACE;MOREMOUNTAINS_TOPDOWNENGINE;MOREMOUNTAINS_INVENTORYENGINE;COZY_WEATHER;COZY_3_AND_UP;COZY_URP;TextMeshPro;ENABLE_LOG;ENABLE_HYBRIDCLR;TIMELINE_FRAMEACCURATE;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER;UNITY_EDITOR_ONLY_COMPILATION</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp/bin/Release/</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.22</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Editor:5</UnityProjectType>
    <UnityBuildTarget>iOS:9</UnityBuildTarget>
    <UnityVersion>2022.3.52f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="/Users/<USER>/.vscode/extensions/visualstudiotoolsforunity.vstuc-1.1.2/Analyzers/Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="/Users/<USER>/Documents/Unity/PokemonUnity/Pets_SanGuo/Library/PackageCache/dev.yarnspinner.unity@be66087b21/SourceGenerator/YarnSpinner.Unity.SourceCodeGenerator.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/Modes/TimelineInactiveMode.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/IPropertyKeyDataSource.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_Manipulators.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/TrackAction.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/FrameRateDrawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Signals/TreeView/SignalListFactory.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/BasicAssetInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_Selection.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/ActionManager.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/AnimationOffsetMenu.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/AnimatedPropertyUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Move/IMoveItemMode.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/MarkerInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/TimelineKeyboardNavigation.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/TimelineTreeView.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/Drawers/AnimationTrackDrawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/IMenuChecked.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/TimelineAssetInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/AddDelete/AddDeleteItemModeMix.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/EditMode.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/TimeFormat.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/TimelineHelpers.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/TrackGui/TimelineTrackBaseGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/TrackModifier.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/KeyTraverser.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/ManipulationsClips.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Signals/Styles.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineMarkerHeaderGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/Modes/TimelineAssetEditionMode.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/Modes/TimelineReadOnlyMode.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/ItemGui/ISelectable.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Signals/SignalManager.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/IRowGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/EditorClipFactory.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/TrackGui/TrackResizeHandle.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/State/SequenceHierarchy.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/Modes/TimelineActiveMode.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Utils/PlacementValidity.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Recording/TrackAssetRecordingExtensions.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/State/ISequenceState.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/PropertyCollector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/ClipInspector/ClipInspectorCurveEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/IMenuName.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Trim/TrimItemModeReplace.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Recording/TimelineRecordingContextualResponder.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Audio/AudioTrackInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/TimeReferenceUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Signals/SignalEventDrawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Attributes/MenuEntryAttribute.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/Invoker.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Attributes/ShortcutAttribute.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/TrackAssetInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/TimelineDragging.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/TimelineClipGroup.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/TimeAreaAutoPanner.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Properties/AssemblyInfo.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/OverlayDrawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/TimelineActions.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_PlayableLookup.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/CustomTrackDrawerAttribute.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_Duration.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/State/SequencePath.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/CurvesOwner/CurvesOwnerInspectorHelper.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/TimelineDataSource.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Signals/TreeView/SignalReceiverTreeView.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/CurveTreeViewNode.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/Scopes/GUIGroupScope.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/ClipAction.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/Snapping/IAttractable.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/TimelineTreeViewGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/DirectorStyles.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/Drawers/ClipDrawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/ObjectReferenceField.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Move/MoveItemModeMix.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/AnimationTrackKeyDataSource.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/Modes/TimelineDisabledMode.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/SequenceContext.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_Breadcrumbs.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Extensions/AnimatedParameterExtensions.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/FileUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_ActiveTimeline.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Trim/ITrimItemMode.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/CustomEditors/TrackEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/AnimationClipActions.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/MarkerActions.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/TrackGui/TimelineTrackErrorGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/ViewModel/TimelineAssetViewModel_versions.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_PlaybackControls.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/Drawers/InfiniteTrackDrawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/AnimationClipCurveCache.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/ViewModel/ScriptableObjectViewPrefs.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/EditModeInputHandler.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_StateChange.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/CustomEditors/ClipEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/CurveDataSource.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/CustomEditors/MarkerEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/TimelinePreferences.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/TimelineAction.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/TrackGui/TimelineTrackGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/Drawers/Layers/MarkersLayer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/BindingTreeViewDataSourceGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/TimeIndicator.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Sequence/RectangleTool.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/SpacePartitioner.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Move/MovingItems.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Items/MarkerItem.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Undo/ApplyDefaultUndoAttribute.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/Control.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Signals/SignalEmitterInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_Navigator.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Undo/UndoScope.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/TimelineSelection.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Utils/ManipulatorsUtils.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindowTimeControl.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/ControlPlayableUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/ClipInspector/ClipInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/Graphics.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/TrackPropertyCurvesDataSource.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Analytics/TimelineAnalytics.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Signals/SignalReceiverHeader.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_PlayRange.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Activation/ActivationTrackEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Signals/SignalEmitterEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_Gui.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_PreviewPlayMode.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/ClipsActions.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/EditorClip.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_EditorCallbacks.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/Scopes/GUIColorOverride.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Extensions/TrackExtensions.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/IInspectorChangeHandler.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/ItemGui/TimelineClipGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/AddDelete/AddDeleteItemModeRipple.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/Scopes/HorizontalScope.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Activation/ActivationTrackInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/WindowConstants.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/TimelineClipHandle.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/ManipulationsTimeline.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Move/MoveItemModeRipple.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Sequence/RectangleSelect.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/TrackGui/InlineCurveEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/Menus/MenuItemActionBase.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/Snapping/ISnappable.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineNavigator.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/TrackActions.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_TrackGui.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Tooltip.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Sequence/TrimClip.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/Menus/TimelineContextMenu.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/DirectorNamedColor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/ManipulationsTracks.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Items/ItemsGroup.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/AddDelete/AddDeleteItemModeReplace.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Sequence/EaseClip.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/TimeFieldDrawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/PreviewedBindings.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/StyleNormalColorOverride.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/ViewModel/TimelineAssetViewModel.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Playables/ControlPlayableInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/Scopes/IndentLevelScope.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Undo/UndoExtensions.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/AnimationTrackActions.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/Drawers/Layers/ItemsLayer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Items/ItemsUtils.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/IAction.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/ItemGui/TimelineMarkerGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/CurvesProxy.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Cursors/TimelineCursors.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/BuiltInCurvePresets.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/AnimationPlayableAssetEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Trim/TrimItemModeMix.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineEditorWindow.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/Drawers/Layers/ClipsLayer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/AnimatedParameterUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Items/ITimelineItem.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/State/PlayRange.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/ClipCurveEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Sequence/RectangleZoom.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Items/ItemsPerTrack.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/BreadcrumbDrawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/TypeUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Audio/AudioClipPropertiesDrawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/PickerUtils.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Audio/AudioPlayableAssetInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Sequence/Jog.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/AddDelete/IAddDeleteItemMode.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/GroupTrackInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/StyleManager.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/Scopes/PropertyScope.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/TimelineInspectorUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/AnimationPlayableAssetInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/Snapping/SnapEngine.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/CustomEditors/CustomTimelineEditorCache.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/ClipModifier.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Move/MoveItemModeReplace.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/TimelineProjectSettings.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/Scopes/GUIViewportScope.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/HeaderSplitterManipulator.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/TimelineClipUnion.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/ViewModel/TimelineWindowViewPrefs.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindowAnalytics.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Signals/SignalReceiverInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/Manipulator.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/Modes/TimelineMode.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/ItemGui/TimelineItemGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/BindingUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Attributes/ActiveInModeAttribute.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelinePlaybackControls.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/CustomEditors/MarkerTrackEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/State/WindowState.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/ActionContext.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/Range.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Attributes/TimelineShortcutAttribute.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Recording/TimelineRecording.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/Drawers/TrackDrawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/ControlTrack/ControlPlayableAssetEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/State/SequenceState.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/ObjectExtension.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Actions/MarkerAction.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/Clipboard.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Sequence/TrackZoom.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/BindingSelector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/DisplayNameHelper.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/Scopes/LabelWidthScope.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Signals/SignalUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/TimelineAnimationUtilities.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Move/MoveItemHandler.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/CurveEditUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/TrackGui/TimelineGroupGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/AnimationClipExtensions.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Utils/EditModeUtils.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/MenuPriority.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Trim/TrimItemModeRipple.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_HeaderGui.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Extensions/AnimationTrackExtensions.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/AnimatedParameterCache.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/ClipInspector/ClipInspectorSelectionInfo.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/FrameRateDisplayUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Sequence/SelectAndMoveItem.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Recording/TimelineRecording_PlayableAsset.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Animation/BindingTreeViewDataSource.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Signals/TreeView/SignalReceiverItem.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Recording/TimelineRecording_Monobehaviour.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_TimeArea.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Sequence/MarkerHeaderTrackManipulator.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Audio/AudioPlayableAssetEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/AnimationTrackInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/TimelineUtility.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/DirectorNamedColorInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Trackhead.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Localization/Localization.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/UnityEditorInternals.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/Modes/TimeReferenceMode.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/PlaybackScroller.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Items/ClipItem.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/ItemGui/TimelineMarkerClusterGUI.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/MarkerModifier.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Utils/EditModeRippleUtils.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/TrackResourceCache.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/Scopes/GUIMixedValueScope.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Recording/AnimationTrackRecorder.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/treeview/Drawers/TrackItemsDrawer.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Utilities/SequenceSelectorNameFormater.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/TimelineEditor.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Utils/EditModeReplaceUtils.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Shortcuts.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Utils/EditModeMixUtils.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Signals/SignalAssetInspector.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Manipulators/Utils/EditModeGUIUtils.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/inspectors/CurvesOwner/ICurvesOwnerInspectorWrapper.cs" />
    <Compile Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Window/TimelineWindow_TimeCursor.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/Unity.Timeline.Editor.asmdef" />
    <None Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/StyleSheets/res/Timeline_LightSkin.txt" />
    <None Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/StyleSheets/Extensions/dark.uss" />
    <None Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/StyleSheets/res/Timeline_DarkSkin.txt" />
    <None Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/StyleSheets/Extensions/light.uss" />
    <None Include="Library/PackageCache/com.unity.timeline@1.7.7/Editor/StyleSheets/Extensions/common.uss" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.NVIDIAModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.NVIDIAModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ProfilerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/Managed/UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.OSXStandalone.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.Protobuf">
      <HintPath>Assets/AssetRaw/DLL/Google.Protobuf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Enrichers.WithCaller">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Enrichers.WithCaller.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.YamlDotNet">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Dependencies/YamlDotNet/Unity.VisualScripting.YamlDotNet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CodeAnalysis">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Editor/Analysis/DLLs/Microsoft.CodeAnalysis.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Satori">
      <HintPath>Assets/Plugins/Nakama/Runtime/Plugins/Satori.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog">
      <HintPath>Assets/AssetRaw/DLL/Serilog.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.System.Text.Encodings.Web">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.System.Text.Encodings.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource">
      <HintPath>Assets/AssetRaw/DLL/System.Diagnostics.DiagnosticSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library/PackageCache/com.unity.collections@2.5.1/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="YarnSpinner.Compiler">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/YarnSpinner.Compiler.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library/PackageCache/com.unity.ext.nunit@2.0.3/net40/unity-custom/nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.NET.StringTools">
      <HintPath>Assets/AssetRaw/DLL/Microsoft.NET.StringTools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ReportGeneratorMerged">
      <HintPath>Library/PackageCache/com.unity.testtools.codecoverage@1.2.6/lib/ReportGenerator/ReportGeneratorMerged.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets/TEngine/Libraries/System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Sinks.Console">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Sinks.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Immutable">
      <HintPath>Assets/AssetRaw/DLL/System.Collections.Immutable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MessagePack">
      <HintPath>Assets/AssetRaw/DLL/MessagePack.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MessagePack.Annotations">
      <HintPath>Assets/AssetRaw/DLL/MessagePack.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Metadata">
      <HintPath>Assets/AssetRaw/DLL/System.Reflection.Metadata.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="unityplastic">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/unityplastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Antlr3.Runtime">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/Unity.Plastic.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets/Plugins/DOTween/DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nakama">
      <HintPath>Assets/Plugins/Nakama/Runtime/Plugins/Nakama.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Ben.Demystifier">
      <HintPath>Assets/AssetRaw/DLL/Ben.Demystifier.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="YarnSpinner">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/YarnSpinner.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Plastic.Newtonsoft.Json">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/Unity.Plastic.Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Sinks.Debug">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Sinks.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="log4netPlastic">
      <HintPath>Library/PackageCache/com.unity.collab-proxy@2.5.2/Lib/Editor/PlasticSCM/log4netPlastic.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.IonicZip">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/Dependencies/DotNetZip/Unity.VisualScripting.IonicZip.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Assets/AssetRaw/DLL/Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="dnlib">
      <HintPath>Library/PackageCache/com.code-philosophy.hybridclr@9429a4d24d/Plugins/dnlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LZ4">
      <HintPath>Library/PackageCache/com.code-philosophy.hybridclr@9429a4d24d/Plugins/LZ4.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>Assets/Plugins/DOTween/Editor/DOTweenEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS_I2Loc.Xcode">
      <HintPath>Assets/TEngine/Editor/Localization/Unity XCode/UnityEditor.iOS_I2Loc.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1/Runtime/Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.TextureAssets">
      <HintPath>Library/PackageCache/com.unity.visualscripting@1.9.4/Editor/VisualScripting.Core/EditorAssetResources/Unity.VisualScripting.TextureAssets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library/PackageCache/com.unity.nuget.mono-cecil@1.11.4/Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.System.Threading.Tasks.Extensions">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.Microsoft.Extensions.FileSystemGlobbing">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.Microsoft.Extensions.FileSystemGlobbing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Channels">
      <HintPath>Assets/AssetRaw/DLL/System.Threading.Channels.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Yarn.System.Text.Json">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Runtime/DLLs/Yarn.System.Text.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CodeAnalysis.CSharp">
      <HintPath>Library/PackageCache/dev.yarnspinner.unity@be66087b21/Editor/Analysis/DLLs/Microsoft.CodeAnalysis.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Serilog.Sinks.File">
      <HintPath>Assets/AssetRaw/DLL/Serilog.Sinks.File.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Common">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>/Applications/Unity/Hub/Editor/2022.3.52f1/Unity.app/Contents/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Unity.Timeline.csproj" />
    <ProjectReference Include="UnityEditor.UI.csproj" />
    <ProjectReference Include="UnityEngine.UI.csproj" />
    <ProjectReference Include="UnityEditor.TestRunner.csproj" />
    <ProjectReference Include="UnityEngine.TestRunner.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
